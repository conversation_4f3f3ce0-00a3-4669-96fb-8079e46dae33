"""
AI endpoints for kitchen sequencing.
"""

import logging
from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.connection import get_db_session
from app.services.ai_sequencer import ai_sequencer
from app.services.data_seeder import data_seeder

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/seed-database")
async def seed_database(db: AsyncSession = Depends(get_db_session)):
    """Seed database from Excel file."""
    try:
        await data_seeder.seed_from_excel(db)
        return {"message": "Database seeded successfully"}
    except Exception as e:
        logger.error(f"Failed to seed database: {e}")
        raise HTTPException(status_code=500, detail="Failed to seed database")

@router.post("/train")
async def train_ai_model(db: AsyncSession = Depends(get_db_session)):
    """Train AI model with kitchen data."""
    try:
        success = await ai_sequencer.train_model(db)
        if success:
            return {"message": "AI model trained successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to train AI model")
    except Exception as e:
        logger.error(f"Failed to train AI: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sequence/{order_id}")
async def sequence_order(
    order_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """Generate AI sequence for order."""
    try:
        result = await ai_sequencer.sequence_order(db, order_id)
        if "error" in result:
            raise HTTPException(status_code=404, detail=result["error"])
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to sequence order: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/training-data")
async def get_training_data(db: AsyncSession = Depends(get_db_session)):
    """Get current training data."""
    try:
        data = await ai_sequencer.get_training_data(db)
        return {"training_data": data}
    except Exception as e:
        logger.error(f"Failed to get training data: {e}")
        raise HTTPException(status_code=500, detail=str(e))