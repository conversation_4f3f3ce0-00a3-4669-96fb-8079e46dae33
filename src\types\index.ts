// Core Types for KDS Queue Management System

export interface CreateOrderRequest {
  customerName?: string;
  tableNumber?: string;
  items: OrderItemRequest[];
  priority?: PriorityLevel;
  notes?: string;
}

export interface OrderItemRequest {
  itemId: string;
  quantity: number;
  notes?: string;
}

export interface UpdateOrderRequest {
  status?: OrderStatus;
  priority?: PriorityLevel;
  notes?: string;
}

export interface CompleteItemRequest {
  itemId: string;
  actualCompletionTime?: Date;
  notes?: string;
}

export interface KitchenStatusUpdateRequest {
  status: KitchenStatus;
  capacity?: number;
}

// Enums
export enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum ItemStatus {
  QUEUED = 'QUEUED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

export enum PriorityLevel {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  EMERGENCY = 'EMERGENCY'
}

export enum DifficultyLevel {
  EASY = 'EASY',
  MEDIUM = 'MEDIUM',
  HARD = 'HARD',
  EXPERT = 'EXPERT'
}

export enum KitchenStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  MAINTENANCE = 'MAINTENANCE'
}

// Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface QueueStatusResponse {
  kitchenId: string;
  kitchenName: string;
  status: KitchenStatus;
  capacity: number;
  currentLoad: number;
  items: QueueItemResponse[];
  estimatedCompletionTime: string;
  averageWaitTime: number;
}

export interface QueueItemResponse {
  id: string;
  itemId: string;
  itemName: string;
  orderId: string;
  status: ItemStatus;
  priority: PriorityLevel;
  prepTime: number;
  sequenceNumber?: number;
  scheduledStart: string;
  estimatedCompletion: string;
  actualStart?: string;
  actualCompletion?: string;
  starvationCount: number;
  notes?: string;
}

export interface OrderResponse {
  id: string;
  orderId: string;
  customerName?: string;
  tableNumber?: string;
  status: OrderStatus;
  priority: PriorityLevel;
  totalAmount?: number;
  estimatedTime?: number;
  actualTime?: number;
  items: OrderItemResponse[];
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface OrderItemResponse {
  id: string;
  menuItemId: string;
  itemName: string;
  quantity: number;
  notes?: string;
}

export interface MenuItemResponse {
  id: string;
  itemId: string;
  itemName: string;
  kitchenId: string;
  prepTimeMinutes: number;
  difficultyLevel: DifficultyLevel;
  available: boolean;
  category?: string;
  description?: string;
  price?: number;
  allergens: string[];
}

export interface KitchenResponse {
  id: string;
  kitchenId: string;
  kitchenName: string;
  capacity: number;
  status: KitchenStatus;
  location?: string;
  equipment: string[];
  currentLoad: number;
  averageWaitTime: number;
}

export interface PerformanceMetrics {
  kitchenId: string;
  averageEfficiency: number;
  totalOrdersCompleted: number;
  averageCompletionTime: number;
  onTimeDeliveryRate: number;
  peakHours: number[];
  bottlenecks: string[];
}

export interface SystemHealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  services: {
    database: ServiceHealth;
    redis: ServiceHealth;
    aiService: ServiceHealth;
  };
  metrics: {
    activeOrders: number;
    totalQueueItems: number;
    averageProcessingTime: number;
    systemUptime: number;
  };
}

export interface ServiceHealth {
  status: 'up' | 'down' | 'degraded';
  responseTime?: number;
  lastCheck: string;
  error?: string;
}

// AI Service Types
export interface AISequenceRequest {
  kitchenId: string;
  queueItems: QueueItemForAI[];
  kitchenCapacity: number;
  currentTime: string;
}

export interface QueueItemForAI {
  id: string;
  itemId: string;
  orderId: string;
  priority: PriorityLevel;
  prepTime: number;
  scheduledStart: string;
  starvationCount: number;
  createdAt: string;
}

export interface AISequenceResponse {
  success: boolean;
  sequencedItems: SequencedItem[];
  reasoning?: string;
  estimatedCompletionTime: string;
  recommendations?: string[];
}

export interface SequencedItem {
  id: string;
  sequenceNumber: number;
  scheduledStart: string;
  estimatedCompletion: string;
  priority: PriorityLevel;
  reasoning?: string;
}

// WebSocket Event Types
export interface WebSocketEvent {
  type: string;
  data: any;
  timestamp: string;
}

export interface QueueUpdateEvent extends WebSocketEvent {
  type: 'queue_update';
  data: {
    kitchenId: string;
    queueStatus: QueueStatusResponse;
  };
}

export interface OrderStatusEvent extends WebSocketEvent {
  type: 'order_status';
  data: {
    orderId: string;
    status: OrderStatus;
    estimatedTime?: number;
  };
}

export interface KitchenStatusEvent extends WebSocketEvent {
  type: 'kitchen_status';
  data: {
    kitchenId: string;
    status: KitchenStatus;
    capacity: number;
    currentLoad: number;
  };
}

// Configuration Types
export interface AppConfig {
  port: number;
  nodeEnv: string;
  apiPrefix: string;
  corsOrigin: string;
  jwtSecret: string;
  logLevel: string;
  logFile: string;
}

export interface DatabaseConfig {
  url: string;
}

export interface RedisConfig {
  url: string;
  password?: string;
  ttl: number;
}

export interface AIServiceConfig {
  url: string;
  timeout: number;
}

export interface QueueConfig {
  maxQueueSize: number;
  sequenceInterval: number;
  aiTriggerThreshold: number;
  starvationThreshold: number;
}

export interface KitchenCapacityConfig {
  grill: number;
  fryer: number;
  cold: number;
  bar: number;
}
