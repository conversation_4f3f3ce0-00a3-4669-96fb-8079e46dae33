"""
Seed database from Excel file.
"""

import logging
from sqlalchemy.ext.asyncio import AsyncSession
from app.services.excel_reader import excel_reader
from app.database.models import Kitchen, MenuItem

logger = logging.getLogger(__name__)

class DataSeeder:
    async def seed_from_excel(self, db: AsyncSession):
        """Seed database from Excel file."""
        try:
            data = excel_reader.read_kitchen_config()
            
            # Seed kitchens
            if 'kitchens' in data or 'Kitchen' in data:
                kitchen_data = data.get('kitchens', data.get('Kitchen', []))
                for row in kitchen_data:
                    kitchen = Kitchen(
                        kitchen_id=str(row.get('kitchen_id', row.get('Kitchen_ID', ''))),
                        kitchen_name=str(row.get('kitchen_name', row.get('Kitchen_Name', ''))),
                        capacity=int(row.get('capacity', row.get('Capacity', 3))),
                        specialization=str(row.get('specialization', row.get('Specialization', ''))),
                        location=str(row.get('location', row.get('Location', ''))),
                        equipment=str(row.get('equipment', row.get('Equipment', '')))
                    )
                    db.add(kitchen)
            
            # Seed menu items
            if 'menu_items' in data or 'Menu' in data:
                menu_data = data.get('menu_items', data.get('Menu', []))
                for row in menu_data:
                    item = MenuItem(
                        item_id=str(row.get('item_id', row.get('Item_ID', ''))),
                        item_name=str(row.get('item_name', row.get('Item_Name', ''))),
                        kitchen_id=str(row.get('kitchen_id', row.get('Kitchen_ID', ''))),
                        prep_time_minutes=int(row.get('prep_time_minutes', row.get('Prep_Time', 10))),
                        difficulty_level=str(row.get('difficulty_level', row.get('Difficulty', 'medium'))),
                        category=str(row.get('category', row.get('Category', ''))),
                        price=float(row.get('price', row.get('Price', 0))),
                        ingredients=str(row.get('ingredients', row.get('Ingredients', ''))),
                        allergens=str(row.get('allergens', row.get('Allergens', '')))
                    )
                    db.add(item)
            
            await db.commit()
            logger.info("Database seeded successfully from Excel")
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Failed to seed database: {e}")
            raise

data_seeder = DataSeeder()