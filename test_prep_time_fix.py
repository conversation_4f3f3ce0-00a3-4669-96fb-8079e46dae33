#!/usr/bin/env python3
"""
Test script to verify the preparation time calculation fixes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.prep_time_calculator import PrepTimeCalculator

def test_verification_scenario():
    """Test the verification scenario from the requirements"""
    print("Testing Verification Scenario:")
    print("Order with:")
    print("- Kitchen 1 (Main): 2 items, prep times 3 and 4 mins")
    print("- Kitchen 2: 6 items, each 5 mins")
    print("- Kitchen 3: 1 item, 7 mins")
    print("- Assume no active in-progress backlog")
    print()
    
    # Create test order items
    order_items = [
        # Kitchen 1 items
        {"kitchen_id": "1", "prep_time": 3, "item_id": "item1"},
        {"kitchen_id": "1", "prep_time": 4, "item_id": "item2"},
        # Kitchen 2 items
        {"kitchen_id": "2", "prep_time": 5, "item_id": "item3"},
        {"kitchen_id": "2", "prep_time": 5, "item_id": "item4"},
        {"kitchen_id": "2", "prep_time": 5, "item_id": "item5"},
        {"kitchen_id": "2", "prep_time": 5, "item_id": "item6"},
        {"kitchen_id": "2", "prep_time": 5, "item_id": "item7"},
        {"kitchen_id": "2", "prep_time": 5, "item_id": "item8"},
        # Kitchen 3 item
        {"kitchen_id": "3", "prep_time": 7, "item_id": "item9"},
    ]
    
    # No active items
    active_items_by_kitchen = {"1": 0, "2": 0, "3": 0}
    
    # Calculate prep times
    result = PrepTimeCalculator.calculate_order_prep_time(order_items, active_items_by_kitchen)
    
    print("Results:")
    print(f"Kitchen 1 prep time: {result['kitchen_prep_times'].get('1', 0)} mins")
    print(f"Kitchen 2 prep time: {result['kitchen_prep_times'].get('2', 0)} mins")
    print(f"Kitchen 3 prep time: {result['kitchen_prep_times'].get('3', 0)} mins")
    print(f"Overall order prep time: {result['total_prep_time']} mins")
    print()
    
    # Expected results
    expected_kitchen_1 = 4  # max(3,4) = 4 mins
    expected_kitchen_2 = 10  # batches of 5 (5 mins) + 1 (5 mins) = 10 mins
    expected_kitchen_3 = 7  # 7 mins
    expected_total = 10  # max(4,10,7) = 10 mins
    
    print("Expected results:")
    print(f"Kitchen 1 prep = max(3,4) = {expected_kitchen_1} mins")
    print(f"Kitchen 2: batches of 5 (5 mins) + 1 (5 mins) = {expected_kitchen_2} mins")
    print(f"Kitchen 3 prep = {expected_kitchen_3} mins")
    print(f"Overall order prep time = max(4,10,7) = {expected_total} mins")
    print()
    
    # Verify results
    success = True
    if result['kitchen_prep_times'].get('1', 0) != expected_kitchen_1:
        print(f"[FAIL] Kitchen 1 FAILED: got {result['kitchen_prep_times'].get('1', 0)}, expected {expected_kitchen_1}")
        success = False
    else:
        print(f"[PASS] Kitchen 1 PASSED: {result['kitchen_prep_times'].get('1', 0)} mins")
    
    if result['kitchen_prep_times'].get('2', 0) != expected_kitchen_2:
        print(f"[FAIL] Kitchen 2 FAILED: got {result['kitchen_prep_times'].get('2', 0)}, expected {expected_kitchen_2}")
        success = False
    else:
        print(f"[PASS] Kitchen 2 PASSED: {result['kitchen_prep_times'].get('2', 0)} mins")
    
    if result['kitchen_prep_times'].get('3', 0) != expected_kitchen_3:
        print(f"[FAIL] Kitchen 3 FAILED: got {result['kitchen_prep_times'].get('3', 0)}, expected {expected_kitchen_3}")
        success = False
    else:
        print(f"[PASS] Kitchen 3 PASSED: {result['kitchen_prep_times'].get('3', 0)} mins")
    
    if result['total_prep_time'] != expected_total:
        print(f"[FAIL] Total FAILED: got {result['total_prep_time']}, expected {expected_total}")
        success = False
    else:
        print(f"[PASS] Total PASSED: {result['total_prep_time']} mins")
    
    return success

def test_kitchen_specific_calculation():
    """Test kitchen-specific calculation"""
    print("\n" + "="*50)
    print("Testing Kitchen-Specific Calculation:")
    
    order_items = [
        {"kitchen_id": "1", "prep_time": 3, "item_id": "item1"},
        {"kitchen_id": "1", "prep_time": 4, "item_id": "item2"},
        {"kitchen_id": "2", "prep_time": 5, "item_id": "item3"},
        {"kitchen_id": "2", "prep_time": 5, "item_id": "item4"},
    ]
    
    # Test kitchen 1 specific time
    kitchen_1_time = PrepTimeCalculator.get_kitchen_completion_time("1", order_items, {"1": 0})
    kitchen_2_time = PrepTimeCalculator.get_kitchen_completion_time("2", order_items, {"2": 0})
    
    print(f"Kitchen 1 specific time: {kitchen_1_time} mins (expected: 4)")
    print(f"Kitchen 2 specific time: {kitchen_2_time} mins (expected: 5)")
    
    success = kitchen_1_time == 4 and kitchen_2_time == 5
    if success:
        print("[PASS] Kitchen-specific calculations PASSED")
    else:
        print("[FAIL] Kitchen-specific calculations FAILED")
    
    return success

def test_queue_waiting_logic():
    """Test that 6th order waits for slot to become available"""
    print("\n" + "="*50)
    print("Testing Queue Waiting Logic:")
    
    # Test case: 6 items, each 10 minutes
    order_items = [
        {"kitchen_id": "1", "prep_time": 10, "item_id": "item1"},
        {"kitchen_id": "1", "prep_time": 10, "item_id": "item2"},
        {"kitchen_id": "1", "prep_time": 10, "item_id": "item3"},
        {"kitchen_id": "1", "prep_time": 10, "item_id": "item4"},
        {"kitchen_id": "1", "prep_time": 10, "item_id": "item5"},
        {"kitchen_id": "1", "prep_time": 10, "item_id": "item6"},  # This should wait
    ]
    
    # Test with no active items
    result = PrepTimeCalculator.calculate_order_prep_time(order_items, {"1": 0})
    kitchen_time = result['kitchen_prep_times']['1']
    print(f"6 items (10 mins each): {kitchen_time} mins")
    print("Expected: First 5 items start immediately (finish at 10 mins), 6th item waits and finishes at 20 mins")
    
    # Test with 3 active items (2 slots available)
    result_active = PrepTimeCalculator.calculate_order_prep_time(order_items, {"1": 3})
    kitchen_time_active = result_active['kitchen_prep_times']['1']
    print(f"3 active + 6 new items: {kitchen_time_active} mins")
    print("Expected: 2 items start now, others wait for slots to free up")
    
    # Verify 6th item waits (should be 20 mins, not 10)
    success = kitchen_time == 20 and kitchen_time_active > 15
    
    if success:
        print("[PASS] Queue waiting logic PASSED - 6th item waits for slot")
    else:
        print(f"[FAIL] Queue waiting logic FAILED - Expected 20 mins, got {kitchen_time}")
    
    return success

if __name__ == "__main__":
    print("Testing Preparation Time Calculation Fixes")
    print("="*50)
    
    all_tests_passed = True
    
    # Run tests
    all_tests_passed &= test_verification_scenario()
    all_tests_passed &= test_kitchen_specific_calculation()
    all_tests_passed &= test_queue_waiting_logic()
    
    print("\n" + "="*50)
    if all_tests_passed:
        print("ALL TESTS PASSED! Preparation time fixes are working correctly.")
    else:
        print("SOME TESTS FAILED! Please review the implementation.")
    
    print("="*50)