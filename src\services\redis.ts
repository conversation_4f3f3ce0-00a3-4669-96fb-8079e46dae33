import { createClient, RedisClientType } from 'redis';
import { redisConfig } from '@/config';
import { redisLogger } from '@/utils/logger';
import { QueueStatusResponse, QueueItemResponse } from '@/types';

class RedisService {
  private static instance: RedisService;
  private client: RedisClientType;
  private isConnected: boolean = false;

  private constructor() {
    this.client = createClient({
      url: redisConfig.url,
      password: redisConfig.password,
      socket: {
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            redisLogger.error('Redis reconnection failed after 10 attempts');
            return false;
          }
          return Math.min(retries * 100, 3000);
        }
      }
    });

    this.setupEventHandlers();
  }

  public static getInstance(): RedisService {
    if (!RedisService.instance) {
      RedisService.instance = new RedisService();
    }
    return RedisService.instance;
  }

  private setupEventHandlers(): void {
    this.client.on('connect', () => {
      redisLogger.info('Redis client connected');
    });

    this.client.on('ready', () => {
      this.isConnected = true;
      redisLogger.info('Redis client ready');
    });

    this.client.on('error', (error) => {
      this.isConnected = false;
      redisLogger.error('Redis client error', { error: error.message });
    });

    this.client.on('end', () => {
      this.isConnected = false;
      redisLogger.info('Redis client connection ended');
    });

    this.client.on('reconnecting', () => {
      redisLogger.info('Redis client reconnecting');
    });
  }

  public async connect(): Promise<void> {
    try {
      await this.client.connect();
      redisLogger.info('Redis connected successfully');
    } catch (error) {
      redisLogger.error('Failed to connect to Redis', { error });
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.client.disconnect();
      this.isConnected = false;
      redisLogger.info('Redis disconnected successfully');
    } catch (error) {
      redisLogger.error('Error disconnecting from Redis', { error });
      throw error;
    }
  }

  public async healthCheck(): Promise<{ status: string; responseTime: number }> {
    const startTime = Date.now();
    try {
      await this.client.ping();
      const responseTime = Date.now() - startTime;
      return { status: 'up', responseTime };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      redisLogger.error('Redis health check failed', { error });
      return { status: 'down', responseTime };
    }
  }

  public get isReady(): boolean {
    return this.isConnected;
  }

  // Queue Management Methods
  public async setQueueStatus(kitchenId: string, queueStatus: QueueStatusResponse): Promise<void> {
    try {
      const key = `queue:${kitchenId}`;
      await this.client.setEx(key, redisConfig.ttl, JSON.stringify(queueStatus));
      redisLogger.debug('Queue status cached', { kitchenId });
    } catch (error) {
      redisLogger.error('Failed to cache queue status', { kitchenId, error });
      throw error;
    }
  }

  public async getQueueStatus(kitchenId: string): Promise<QueueStatusResponse | null> {
    try {
      const key = `queue:${kitchenId}`;
      const data = await this.client.get(key);
      if (!data) return null;
      
      const queueStatus = JSON.parse(data) as QueueStatusResponse;
      redisLogger.debug('Queue status retrieved from cache', { kitchenId });
      return queueStatus;
    } catch (error) {
      redisLogger.error('Failed to retrieve queue status from cache', { kitchenId, error });
      return null;
    }
  }

  public async deleteQueueStatus(kitchenId: string): Promise<void> {
    try {
      const key = `queue:${kitchenId}`;
      await this.client.del(key);
      redisLogger.debug('Queue status removed from cache', { kitchenId });
    } catch (error) {
      redisLogger.error('Failed to remove queue status from cache', { kitchenId, error });
      throw error;
    }
  }

  // Queue Items Management
  public async addQueueItem(kitchenId: string, queueItem: QueueItemResponse): Promise<void> {
    try {
      const key = `queue_items:${kitchenId}`;
      await this.client.lPush(key, JSON.stringify(queueItem));
      await this.client.expire(key, redisConfig.ttl);
      redisLogger.debug('Queue item added to cache', { kitchenId, itemId: queueItem.itemId });
    } catch (error) {
      redisLogger.error('Failed to add queue item to cache', { kitchenId, error });
      throw error;
    }
  }

  public async getQueueItems(kitchenId: string): Promise<QueueItemResponse[]> {
    try {
      const key = `queue_items:${kitchenId}`;
      const items = await this.client.lRange(key, 0, -1);
      const queueItems = items.map(item => JSON.parse(item) as QueueItemResponse);
      redisLogger.debug('Queue items retrieved from cache', { kitchenId, count: queueItems.length });
      return queueItems;
    } catch (error) {
      redisLogger.error('Failed to retrieve queue items from cache', { kitchenId, error });
      return [];
    }
  }

  public async removeQueueItem(kitchenId: string, itemId: string): Promise<void> {
    try {
      const key = `queue_items:${kitchenId}`;
      const items = await this.client.lRange(key, 0, -1);
      
      for (let i = 0; i < items.length; i++) {
        const item = JSON.parse(items[i]) as QueueItemResponse;
        if (item.itemId === itemId) {
          await this.client.lRem(key, 1, items[i]);
          break;
        }
      }
      
      redisLogger.debug('Queue item removed from cache', { kitchenId, itemId });
    } catch (error) {
      redisLogger.error('Failed to remove queue item from cache', { kitchenId, itemId, error });
      throw error;
    }
  }

  public async clearQueueItems(kitchenId: string): Promise<void> {
    try {
      const key = `queue_items:${kitchenId}`;
      await this.client.del(key);
      redisLogger.debug('Queue items cleared from cache', { kitchenId });
    } catch (error) {
      redisLogger.error('Failed to clear queue items from cache', { kitchenId, error });
      throw error;
    }
  }

  // General Cache Methods
  public async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
      if (ttl) {
        await this.client.setEx(key, ttl, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }
      redisLogger.debug('Value cached', { key });
    } catch (error) {
      redisLogger.error('Failed to cache value', { key, error });
      throw error;
    }
  }

  public async get<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      if (!value) return null;
      
      try {
        return JSON.parse(value) as T;
      } catch {
        return value as T;
      }
    } catch (error) {
      redisLogger.error('Failed to retrieve cached value', { key, error });
      return null;
    }
  }

  public async del(key: string): Promise<void> {
    try {
      await this.client.del(key);
      redisLogger.debug('Cached value deleted', { key });
    } catch (error) {
      redisLogger.error('Failed to delete cached value', { key, error });
      throw error;
    }
  }

  public async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      redisLogger.error('Failed to check key existence', { key, error });
      return false;
    }
  }

  public async expire(key: string, seconds: number): Promise<void> {
    try {
      await this.client.expire(key, seconds);
      redisLogger.debug('Key expiration set', { key, seconds });
    } catch (error) {
      redisLogger.error('Failed to set key expiration', { key, seconds, error });
      throw error;
    }
  }

  // Pattern-based operations
  public async keys(pattern: string): Promise<string[]> {
    try {
      const keys = await this.client.keys(pattern);
      redisLogger.debug('Keys retrieved by pattern', { pattern, count: keys.length });
      return keys;
    } catch (error) {
      redisLogger.error('Failed to retrieve keys by pattern', { pattern, error });
      return [];
    }
  }

  public async deleteByPattern(pattern: string): Promise<number> {
    try {
      const keys = await this.client.keys(pattern);
      if (keys.length === 0) return 0;
      
      const result = await this.client.del(keys);
      redisLogger.debug('Keys deleted by pattern', { pattern, count: result });
      return result;
    } catch (error) {
      redisLogger.error('Failed to delete keys by pattern', { pattern, error });
      return 0;
    }
  }

  // Pub/Sub methods for real-time updates
  public async publish(channel: string, message: any): Promise<void> {
    try {
      const serializedMessage = typeof message === 'string' ? message : JSON.stringify(message);
      await this.client.publish(channel, serializedMessage);
      redisLogger.debug('Message published', { channel });
    } catch (error) {
      redisLogger.error('Failed to publish message', { channel, error });
      throw error;
    }
  }

  // Atomic operations
  public async increment(key: string, by: number = 1): Promise<number> {
    try {
      const result = await this.client.incrBy(key, by);
      redisLogger.debug('Key incremented', { key, by, result });
      return result;
    } catch (error) {
      redisLogger.error('Failed to increment key', { key, by, error });
      throw error;
    }
  }

  public async decrement(key: string, by: number = 1): Promise<number> {
    try {
      const result = await this.client.decrBy(key, by);
      redisLogger.debug('Key decremented', { key, by, result });
      return result;
    } catch (error) {
      redisLogger.error('Failed to decrement key', { key, by, error });
      throw error;
    }
  }
}

// Export singleton instance
export const redisService = RedisService.getInstance();
export default redisService;
