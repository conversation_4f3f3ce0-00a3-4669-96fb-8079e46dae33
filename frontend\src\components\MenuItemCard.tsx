import React from 'react';
import { Plus, Minus } from 'lucide-react';
import { useCart } from '@/contexts/CartContext';

interface MenuItem {
  item_id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image_url: string;
  nutritional_info: {
    calories: number;
    protein?: string;
    carbs?: string;
    fat?: string;
  };
}

interface MenuItemCardProps {
  item: MenuItem;
}

const MenuItemCard = ({ item }: MenuItemCardProps) => {
  const { cartItems, addToCart, updateQuantity } = useCart();

  const cartItem = cartItems.find(cartItem => cartItem.id === item.item_id);
  const quantity = cartItem?.quantity || 0;

  const handleAddToCart = () => {
    addToCart({
      id: item.item_id,
      name: item.name,
      price: item.price,
      image: item.image_url,
      category: item.category,
      calories: item.nutritional_info.calories,
      addOns: [], // Not applicable but may be required by cart context
    });
  };

  const handleQuantityChange = (newQuantity: number) => {
    updateQuantity(item.item_id, newQuantity);
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      <img
        src={item.image_url || '/placeholder.jpg'} // fallback if image_url is empty
        alt={item.name}
        className="w-full h-48 object-cover"
      />

      <div className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
          <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
            {item.nutritional_info?.calories} cal
          </span>
        </div>

        <p className="text-gray-600 text-sm mb-3">{item.description}</p>

        <div className="text-sm text-gray-500 mb-3">
          Category: <span className="capitalize">{item.category}</span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-xl font-bold text-pulse-600">${item.price}</span>

          {quantity === 0 ? (
            <button
              onClick={handleAddToCart}
              className="bg-pulse-500 text-white px-4 py-2 rounded-lg hover:bg-pulse-600 transition-colors flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Add</span>
            </button>
          ) : (
            <div className="flex items-center space-x-3">
              <button
                onClick={() => handleQuantityChange(quantity - 1)}
                className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300 transition-colors"
              >
                <Minus className="w-4 h-4" />
              </button>
              <span className="font-semibold text-lg animate-pulse">{quantity}</span>
              <button
                onClick={() => handleQuantityChange(quantity + 1)}
                className="w-8 h-8 rounded-full bg-pulse-500 text-white flex items-center justify-center hover:bg-pulse-600 transition-colors"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MenuItemCard;
