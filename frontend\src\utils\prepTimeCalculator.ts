/**
 * Frontend Preparation Time Calculator
 * Matches backend logic for consistent calculations
 */

export interface PrepTimeItem {
  item_id: string;
  kitchen_id: string;
  prep_time: number;
  prep_time_minutes?: number;
}

export interface KitchenSlotInfo {
  kitchen_id: string;
  active_items: number;
  slot_availability?: Date[];
}

export interface PrepTimeResult {
  total_prep_time: number;
  kitchen_prep_times: Record<string, number>;
  estimated_completion: Date;
  estimated_completion_ist: string;
  buffer_applied: boolean;
}

export class PrepTimeCalculator {
  private static readonly MAX_PARALLEL_ITEMS = 5;
  private static readonly ORDER_BUFFER_MINUTES = 2;
  private static readonly IST_TIMEZONE = 'Asia/Kolkata';

  static calculateOrderPrepTime(
    orderItems: PrepTimeItem[],
    kitchenSlotInfo: Record<string, KitchenSlotInfo> = {}
  ): PrepTimeResult {
    if (!orderItems || orderItems.length === 0) {
      const now = new Date();
      return {
        total_prep_time: 0,
        kitchen_prep_times: {},
        estimated_completion: now,
        estimated_completion_ist: this.formatISTTime(now),
        buffer_applied: false
      };
    }

    const currentTime = new Date();

    // Single item optimization
    if (orderItems.length === 1) {
      const item = orderItems[0];
      const kitchenId = item.kitchen_id || 'UNKNOWN';
      const prepTime = item.prep_time || item.prep_time_minutes || 10;
      
      const slotInfo = kitchenSlotInfo[kitchenId];
      const kitchenPrepTime = this.calculateSingleItemPrepTime(
        prepTime,
        slotInfo?.active_items || 0,
        slotInfo?.slot_availability || []
      );
      
      // Apply buffer for single item
      const totalWithBuffer = kitchenPrepTime + this.ORDER_BUFFER_MINUTES;
      const completionTime = new Date(currentTime.getTime() + totalWithBuffer * 60000);
      
      return {
        total_prep_time: totalWithBuffer,
        kitchen_prep_times: { [kitchenId]: kitchenPrepTime },
        estimated_completion: completionTime,
        estimated_completion_ist: this.formatISTTime(completionTime),
        buffer_applied: true
      };
    }

    // Group items by kitchen
    const kitchenItems: Record<string, number[]> = {};
    orderItems.forEach(item => {
      const kitchenId = item.kitchen_id || 'UNKNOWN';
      const prepTime = item.prep_time || item.prep_time_minutes || 10;
      
      if (!kitchenItems[kitchenId]) {
        kitchenItems[kitchenId] = [];
      }
      kitchenItems[kitchenId].push(prepTime);
    });

    // Calculate prep time for each kitchen
    const kitchenPrepTimes: Record<string, number> = {};
    Object.entries(kitchenItems).forEach(([kitchenId, prepTimes]) => {
      const slotInfo = kitchenSlotInfo[kitchenId];
      kitchenPrepTimes[kitchenId] = this.calculateKitchenPrepTimeWithSlots(
        prepTimes,
        slotInfo?.active_items || 0,
        slotInfo?.slot_availability || []
      );
    });

    // Total prep time is maximum across all kitchens + buffer
    const maxKitchenTime = Math.max(...Object.values(kitchenPrepTimes));
    const totalWithBuffer = maxKitchenTime + this.ORDER_BUFFER_MINUTES;
    const completionTime = new Date(currentTime.getTime() + totalWithBuffer * 60000);

    return {
      total_prep_time: totalWithBuffer,
      kitchen_prep_times: kitchenPrepTimes,
      estimated_completion: completionTime,
      estimated_completion_ist: this.formatISTTime(completionTime),
      buffer_applied: true
    };
  }

  static getKitchenSpecificPrepTime(
    kitchenId: string,
    orderItems: PrepTimeItem[],
    kitchenSlotInfo: Record<string, KitchenSlotInfo> = {}
  ): {
    kitchen_id: string;
    prep_time: number;
    estimated_completion: Date;
    estimated_completion_ist: string;
    items_count: number;
  } {
    const kitchenItems = orderItems.filter(item => item.kitchen_id === kitchenId);
    
    if (!kitchenItems.length) {
      const now = new Date();
      return {
        kitchen_id: kitchenId,
        prep_time: 0,
        estimated_completion: now,
        estimated_completion_ist: this.formatISTTime(now),
        items_count: 0
      };
    }

    const prepTimes = kitchenItems.map(item => item.prep_time || item.prep_time_minutes || 10);
    const slotInfo = kitchenSlotInfo[kitchenId];
    
    const kitchenPrepTime = this.calculateKitchenPrepTimeWithSlots(
      prepTimes,
      slotInfo?.active_items || 0,
      slotInfo?.slot_availability || []
    );
    
    const currentTime = new Date();
    const completionTime = new Date(currentTime.getTime() + kitchenPrepTime * 60000);

    return {
      kitchen_id: kitchenId,
      prep_time: kitchenPrepTime,
      estimated_completion: completionTime,
      estimated_completion_ist: this.formatISTTime(completionTime),
      items_count: kitchenItems.length
    };
  }

  private static calculateSingleItemPrepTime(
    prepTime: number,
    activeItems: number,
    slotAvailability: Date[]
  ): number {
    if (activeItems < this.MAX_PARALLEL_ITEMS) {
      // Slot available, item starts immediately
      return prepTime;
    }

    // All slots occupied, find earliest available slot
    if (slotAvailability.length > 0) {
      const earliestAvailable = new Date(Math.min(...slotAvailability.map(d => d.getTime())));
      const currentTime = new Date();
      const waitTime = Math.max(0, Math.floor((earliestAvailable.getTime() - currentTime.getTime()) / 60000));
      return waitTime + prepTime;
    }

    // Fallback: assume current items finish in average time
    const estimatedWait = 8;
    return estimatedWait + prepTime;
  }

  private static calculateKitchenPrepTimeWithSlots(
    prepTimes: number[],
    activeItems: number,
    slotAvailability: Date[]
  ): number {
    if (!prepTimes.length) return 0;

    // Sort prep times for optimal batching (longest first)
    const sortedTimes = [...prepTimes].sort((a, b) => b - a);
    const currentTime = new Date();

    // Initialize slot availability times
    const slotTimes = new Array(this.MAX_PARALLEL_ITEMS).fill(currentTime.getTime());

    // Mark occupied slots with their estimated completion times
    if (slotAvailability.length > 0) {
      for (let i = 0; i < Math.min(activeItems, slotAvailability.length); i++) {
        slotTimes[i] = slotAvailability[i].getTime();
      }
    } else {
      // Estimate when current active items will complete
      for (let i = 0; i < activeItems; i++) {
        slotTimes[i] = currentTime.getTime() + (8 * 60000); // 8 minutes average
      }
    }

    // Process items in batches
    let totalCompletionTime = currentTime.getTime();
    const remainingItems = [...sortedTimes];

    while (remainingItems.length > 0) {
      // Find available slots (earliest completion times)
      slotTimes.sort((a, b) => a - b);
      const batchStartTime = slotTimes[0];

      // Determine batch size
      const batchSize = Math.min(remainingItems.length, this.MAX_PARALLEL_ITEMS);
      const batchItems = remainingItems.splice(0, batchSize);

      // Batch completion time is the maximum prep time in the batch
      const batchDuration = Math.max(...batchItems);
      const batchCompletionTime = batchStartTime + (batchDuration * 60000);

      // Update slot availability times
      for (let i = 0; i < batchSize; i++) {
        slotTimes[i] = batchCompletionTime;
      }

      totalCompletionTime = Math.max(totalCompletionTime, batchCompletionTime);
    }

    // Return total time in minutes from now
    return Math.max(0, Math.floor((totalCompletionTime - currentTime.getTime()) / 60000));
  }

  static formatISTTime(date: Date): string {
    return new Intl.DateTimeFormat('en-IN', {
      timeZone: this.IST_TIMEZONE,
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
      timeZoneName: 'short'
    }).format(date);
  }

  static formatTime(minutes: number): string {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  }

  static getISTTime(): Date {
    return new Date(new Date().toLocaleString("en-US", { timeZone: this.IST_TIMEZONE }));
  }
}
