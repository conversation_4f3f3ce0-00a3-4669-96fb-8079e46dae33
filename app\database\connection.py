"""
PostgreSQL database connection and session management.
"""

import os
import logging
from typing import As<PERSON><PERSON>enerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import NullPool
from contextlib import asynccontextmanager

from app.database.models import Base

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages PostgreSQL database connections and sessions."""
    
    def __init__(self):
        self.database_url = os.getenv(
            "DATABASE_URL", 
            "postgresql+asyncpg://postgres:password@localhost:5432/kds_queue_management"
        )
        self.engine = None
        self.async_session_maker = None
        self._connected = False
    
    async def connect(self):
        """Initialize database connection."""
        try:
            # Create async engine
            self.engine = create_async_engine(
                self.database_url,
                echo=os.getenv("DATABASE_ECHO", "false").lower() == "true",
                poolclass=NullPool if os.getenv("DATABASE_POOL", "true").lower() == "false" else None,
                pool_pre_ping=True,
                pool_recycle=3600,  # Recycle connections every hour
            )
            
            # Create session maker
            self.async_session_maker = async_sessionmaker(
                bind=self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Test connection
            async with self.engine.begin() as conn:
                await conn.run_sync(lambda sync_conn: sync_conn.execute("SELECT 1"))
            
            self._connected = True
            logger.info("✅ PostgreSQL database connected successfully")
            
        except Exception as e:
            self._connected = False
            logger.error(f"❌ Failed to connect to PostgreSQL database: {e}")
            raise
    
    async def disconnect(self):
        """Close database connection."""
        if self.engine:
            await self.engine.dispose()
            self._connected = False
            logger.info("PostgreSQL database disconnected")
    
    async def create_tables(self):
        """Create all database tables."""
        if not self.engine:
            raise RuntimeError("Database not connected")
        
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("✅ Database tables created successfully")
        except Exception as e:
            logger.error(f"❌ Failed to create database tables: {e}")
            raise
    
    async def drop_tables(self):
        """Drop all database tables (use with caution!)."""
        if not self.engine:
            raise RuntimeError("Database not connected")
        
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.drop_all)
            logger.info("⚠️ Database tables dropped")
        except Exception as e:
            logger.error(f"❌ Failed to drop database tables: {e}")
            raise
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session with automatic cleanup."""
        if not self._connected or not self.async_session_maker:
            raise RuntimeError("Database not connected")
        
        async with self.async_session_maker() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def health_check(self) -> dict:
        """Check database health."""
        if not self._connected or not self.engine:
            return {
                "status": "disconnected",
                "message": "Database not connected"
            }
        
        try:
            async with self.engine.begin() as conn:
                result = await conn.execute("SELECT version()")
                version = result.scalar()
            
            return {
                "status": "connected",
                "version": version,
                "database_url": self.database_url.split('@')[1] if '@' in self.database_url else "hidden"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }
    
    @property
    def is_connected(self) -> bool:
        """Check if database is connected."""
        return self._connected

# Global database manager instance
db_manager = DatabaseManager()

# Dependency for FastAPI
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency to get database session."""
    async with db_manager.get_session() as session:
        yield session

# Utility functions
async def init_database():
    """Initialize database connection and create tables."""
    await db_manager.connect()
    await db_manager.create_tables()

async def close_database():
    """Close database connection."""
    await db_manager.disconnect()

# Export commonly used items
__all__ = [
    "db_manager",
    "get_db_session", 
    "init_database",
    "close_database",
    "DatabaseManager"
]
