import { PrismaClient } from '@prisma/client';
import { dbLogger, logDatabaseOperation } from '@/utils/logger';

class DatabaseService {
  private static instance: DatabaseService;
  private prisma: PrismaClient;
  private isConnected: boolean = false;

  private constructor() {
    this.prisma = new PrismaClient({
      log: [
        { emit: 'event', level: 'query' },
        { emit: 'event', level: 'error' },
        { emit: 'event', level: 'info' },
        { emit: 'event', level: 'warn' },
      ],
    });

    // Set up logging
    this.setupLogging();
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  private setupLogging(): void {
    this.prisma.$on('query', (e) => {
      logDatabaseOperation('query', e.target || 'unknown', e.duration, {
        query: e.query,
        params: e.params
      });
    });

    this.prisma.$on('error', (e) => {
      dbLogger.error('Database error', {
        target: e.target,
        message: e.message
      });
    });

    this.prisma.$on('info', (e) => {
      dbLogger.info('Database info', {
        target: e.target,
        message: e.message
      });
    });

    this.prisma.$on('warn', (e) => {
      dbLogger.warn('Database warning', {
        target: e.target,
        message: e.message
      });
    });
  }

  public async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      this.isConnected = true;
      dbLogger.info('Database connected successfully');
    } catch (error) {
      this.isConnected = false;
      dbLogger.error('Failed to connect to database', { error });
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      this.isConnected = false;
      dbLogger.info('Database disconnected successfully');
    } catch (error) {
      dbLogger.error('Error disconnecting from database', { error });
      throw error;
    }
  }

  public async healthCheck(): Promise<{ status: string; responseTime: number }> {
    const startTime = Date.now();
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      const responseTime = Date.now() - startTime;
      return { status: 'up', responseTime };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      dbLogger.error('Database health check failed', { error });
      return { status: 'down', responseTime };
    }
  }

  public get client(): PrismaClient {
    if (!this.isConnected) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.prisma;
  }

  public get isReady(): boolean {
    return this.isConnected;
  }

  // Transaction wrapper
  public async transaction<T>(
    fn: (prisma: PrismaClient) => Promise<T>
  ): Promise<T> {
    const startTime = Date.now();
    try {
      const result = await this.prisma.$transaction(fn);
      logDatabaseOperation('transaction', 'multiple', Date.now() - startTime, {
        success: true
      });
      return result;
    } catch (error) {
      logDatabaseOperation('transaction', 'multiple', Date.now() - startTime, {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  // Batch operations
  public async batchExecute(operations: any[]): Promise<any[]> {
    const startTime = Date.now();
    try {
      const results = await this.prisma.$transaction(operations);
      logDatabaseOperation('batch', 'multiple', Date.now() - startTime, {
        operationCount: operations.length,
        success: true
      });
      return results;
    } catch (error) {
      logDatabaseOperation('batch', 'multiple', Date.now() - startTime, {
        operationCount: operations.length,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  // Raw query execution with logging
  public async executeRaw(query: string, params?: any[]): Promise<any> {
    const startTime = Date.now();
    try {
      const result = await this.prisma.$executeRawUnsafe(query, ...(params || []));
      logDatabaseOperation('raw-execute', 'raw', Date.now() - startTime, {
        query,
        success: true
      });
      return result;
    } catch (error) {
      logDatabaseOperation('raw-execute', 'raw', Date.now() - startTime, {
        query,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  public async queryRaw(query: string, params?: any[]): Promise<any> {
    const startTime = Date.now();
    try {
      const result = await this.prisma.$queryRawUnsafe(query, ...(params || []));
      logDatabaseOperation('raw-query', 'raw', Date.now() - startTime, {
        query,
        success: true
      });
      return result;
    } catch (error) {
      logDatabaseOperation('raw-query', 'raw', Date.now() - startTime, {
        query,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  // Utility methods for common operations
  public async findUnique<T>(
    model: string,
    where: any,
    include?: any
  ): Promise<T | null> {
    const startTime = Date.now();
    try {
      // @ts-ignore - Dynamic model access
      const result = await this.prisma[model].findUnique({
        where,
        include
      });
      logDatabaseOperation('findUnique', model, Date.now() - startTime);
      return result;
    } catch (error) {
      logDatabaseOperation('findUnique', model, Date.now() - startTime, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  public async findMany<T>(
    model: string,
    options: any = {}
  ): Promise<T[]> {
    const startTime = Date.now();
    try {
      // @ts-ignore - Dynamic model access
      const result = await this.prisma[model].findMany(options);
      logDatabaseOperation('findMany', model, Date.now() - startTime, {
        resultCount: result.length
      });
      return result;
    } catch (error) {
      logDatabaseOperation('findMany', model, Date.now() - startTime, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  public async create<T>(
    model: string,
    data: any,
    include?: any
  ): Promise<T> {
    const startTime = Date.now();
    try {
      // @ts-ignore - Dynamic model access
      const result = await this.prisma[model].create({
        data,
        include
      });
      logDatabaseOperation('create', model, Date.now() - startTime);
      return result;
    } catch (error) {
      logDatabaseOperation('create', model, Date.now() - startTime, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  public async update<T>(
    model: string,
    where: any,
    data: any,
    include?: any
  ): Promise<T> {
    const startTime = Date.now();
    try {
      // @ts-ignore - Dynamic model access
      const result = await this.prisma[model].update({
        where,
        data,
        include
      });
      logDatabaseOperation('update', model, Date.now() - startTime);
      return result;
    } catch (error) {
      logDatabaseOperation('update', model, Date.now() - startTime, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  public async delete<T>(
    model: string,
    where: any
  ): Promise<T> {
    const startTime = Date.now();
    try {
      // @ts-ignore - Dynamic model access
      const result = await this.prisma[model].delete({ where });
      logDatabaseOperation('delete', model, Date.now() - startTime);
      return result;
    } catch (error) {
      logDatabaseOperation('delete', model, Date.now() - startTime, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();
export default databaseService;
