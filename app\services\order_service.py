"""
Simple order service for managing orders from creation to delivery.
"""

import logging
from typing import List, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from app.database.models import Order as DBOrder, OrderItem as DBOrderItem, OrderStatus
from app.models.base import Order, OrderItem, OrderStatusUpdate

logger = logging.getLogger(__name__)

class OrderService:
    """Service for managing orders."""
    
    async def create_order(self, db: AsyncSession, order_data: Order) -> Order:
        """Create a new order."""
        try:
            # Create order
            db_order = DBOrder(
                order_id=order_data.order_id,
                customer_name=order_data.customer_name,
                table_number=order_data.table_number,
                order_type=order_data.order_type,
                status=OrderStatus.PENDING,
                notes=order_data.notes
            )
            
            db.add(db_order)
            await db.flush()
            
            # Create order items
            for item in order_data.items:
                db_item = DBOrderItem(
                    order_id=order_data.order_id,
                    item_id=item.item_id,
                    item_name=item.item_name,
                    quantity=item.quantity,
                    notes=item.notes
                )
                db.add(db_item)
            
            await db.commit()
            
            # Return created order
            return await self.get_order(db, order_data.order_id)
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Failed to create order {order_data.order_id}: {e}")
            raise
    
    async def get_order(self, db: AsyncSession, order_id: str) -> Optional[Order]:
        """Get order by ID."""
        try:
            result = await db.execute(
                select(DBOrder)
                .options(selectinload(DBOrder.items))
                .where(DBOrder.order_id == order_id)
            )
            db_order = result.scalar_one_or_none()
            
            if not db_order:
                return None
            
            # Convert to Pydantic model
            items = [
                OrderItem(
                    item_id=item.item_id,
                    item_name=item.item_name,
                    quantity=item.quantity,
                    notes=item.notes
                )
                for item in db_order.items
            ]
            
            return Order(
                order_id=db_order.order_id,
                customer_name=db_order.customer_name,
                table_number=db_order.table_number,
                order_type=db_order.order_type,
                items=items,
                status=db_order.status.value,
                notes=db_order.notes,
                created_at=db_order.created_at,
                updated_at=db_order.updated_at
            )
            
        except Exception as e:
            logger.error(f"Failed to get order {order_id}: {e}")
            raise
    
    async def get_orders(self, db: AsyncSession, status: Optional[str] = None, limit: int = 100) -> List[Order]:
        """Get list of orders."""
        try:
            query = select(DBOrder).options(selectinload(DBOrder.items))
            
            if status:
                query = query.where(DBOrder.status == status)
            
            query = query.order_by(DBOrder.created_at.desc()).limit(limit)
            
            result = await db.execute(query)
            db_orders = result.scalars().all()
            
            orders = []
            for db_order in db_orders:
                items = [
                    OrderItem(
                        item_id=item.item_id,
                        item_name=item.item_name,
                        quantity=item.quantity,
                        notes=item.notes
                    )
                    for item in db_order.items
                ]
                
                orders.append(Order(
                    order_id=db_order.order_id,
                    customer_name=db_order.customer_name,
                    table_number=db_order.table_number,
                    order_type=db_order.order_type,
                    items=items,
                    status=db_order.status.value,
                    notes=db_order.notes,
                    created_at=db_order.created_at,
                    updated_at=db_order.updated_at
                ))
            
            return orders
            
        except Exception as e:
            logger.error(f"Failed to get orders: {e}")
            raise
    
    async def update_order_status(self, db: AsyncSession, order_id: str, status_update: OrderStatusUpdate) -> Optional[Order]:
        """Update order status."""
        try:
            # Update order status
            await db.execute(
                update(DBOrder)
                .where(DBOrder.order_id == order_id)
                .values(
                    status=status_update.status,
                    notes=status_update.notes,
                    updated_at=datetime.now()
                )
            )
            
            await db.commit()
            
            # Return updated order
            return await self.get_order(db, order_id)
            
        except Exception as e:
            await db.rollback()
            logger.error(f"Failed to update order {order_id}: {e}")
            raise

# Global service instance
order_service = OrderService()