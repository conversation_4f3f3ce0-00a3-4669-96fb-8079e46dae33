"""
Preparation Time API endpoints
"""

from fastapi import APIRouter, HTTPException
from typing import List, Dict, Optional
from pydantic import BaseModel
from datetime import datetime, timedelta
import pytz

from app.services.prep_time_calculator import PrepTimeCalculator
from app.services.queue_manager import KitchenQueueManager

router = APIRouter(prefix="/api/prep-time", tags=["Preparation Time"])

class PrepTimeRequest(BaseModel):
    items: List[Dict]  # List of items with kitchen_id and prep_time
    active_items_by_kitchen: Optional[Dict[str, int]] = None

class PrepTimeResponse(BaseModel):
    total_prep_time: int
    kitchen_prep_times: Dict[str, int]
    estimated_completion: str  # UTC ISO format
    estimated_completion_ist: str  # IST display format
    current_time_ist: str
    buffer_applied: bool

class KitchenPrepTimeResponse(BaseModel):
    kitchen_id: str
    prep_time: int
    estimated_completion: str  # UTC ISO format
    estimated_completion_ist: str  # IST display format
    current_time_ist: str
    items_count: int

@router.post("/calculate", response_model=PrepTimeResponse)
async def calculate_prep_time(request: PrepTimeRequest):
    """Calculate intelligent preparation time for items"""
    try:
        result = PrepTimeCalculator.calculate_order_prep_time(
            request.items,
            request.active_items_by_kitchen
        )
        
        return PrepTimeResponse(
            total_prep_time=result['total_prep_time'],
            kitchen_prep_times=result['kitchen_prep_times'],
            estimated_completion=result['estimated_completion'].isoformat(),
            estimated_completion_ist=PrepTimeCalculator.format_ist_time(result['estimated_completion']),
            current_time_ist=PrepTimeCalculator.format_ist_time(PrepTimeCalculator.get_utc_now()),
            buffer_applied=result['buffer_applied']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Calculation error: {str(e)}")

@router.get("/kitchen/{kitchen_id}")
async def get_kitchen_prep_time(kitchen_id: str):
    """Get preparation time for a specific kitchen"""
    try:
        queue_manager = KitchenQueueManager()
        estimated_completion = await queue_manager.get_estimated_completion_time(kitchen_id)
        current_time = PrepTimeCalculator.get_ist_now()
        
        prep_time_minutes = max(0, int((estimated_completion - current_time).total_seconds() / 60))
        
        return {
            "kitchen_id": kitchen_id,
            "prep_time_minutes": prep_time_minutes,
            "estimated_completion": estimated_completion.isoformat(),
            "current_time_ist": current_time.isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Kitchen prep time error: {str(e)}")

@router.post("/calculate-enhanced", response_model=PrepTimeResponse)
async def calculate_enhanced_prep_time(request: PrepTimeRequest):
    """Calculate intelligent preparation time with slot management and IST handling"""
    try:
        # Get real-time active items from queue manager
        queue_manager = KitchenQueueManager()
        
        # Get current kitchen loads if not provided
        if not request.active_items_by_kitchen:
            kitchen_statuses = queue_manager.get_all_kitchen_statuses()
            active_items_by_kitchen = {
                status.kitchen_id: status.current_load 
                for status in kitchen_statuses
            }
        else:
            active_items_by_kitchen = request.active_items_by_kitchen
        
        # Get slot availability for better accuracy
        kitchen_slot_availability = {}
        for kitchen_id in set(item.get('kitchen_id') for item in request.items):
            if kitchen_id:
                estimated_completion = await queue_manager.get_estimated_completion_time(kitchen_id)
                kitchen_slot_availability[kitchen_id] = [estimated_completion]
        
        result = PrepTimeCalculator.calculate_order_prep_time(
            request.items,
            active_items_by_kitchen,
            kitchen_slot_availability
        )
        
        return PrepTimeResponse(
            total_prep_time=result['total_prep_time'],
            kitchen_prep_times=result['kitchen_prep_times'],
            estimated_completion=result['estimated_completion'].isoformat(),
            estimated_completion_ist=PrepTimeCalculator.format_ist_time(result['estimated_completion']),
            current_time_ist=PrepTimeCalculator.format_ist_time(PrepTimeCalculator.get_utc_now()),
            buffer_applied=result['buffer_applied']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Enhanced calculation error: {str(e)}")

@router.get("/kitchen/{kitchen_id}/order/{order_id}")
async def get_kitchen_order_prep_time(kitchen_id: str, order_id: str):
    """Get preparation time for a specific kitchen's items in an order"""
    try:
        queue_manager = KitchenQueueManager()
        
        # Get order items for this kitchen
        order_items = await queue_manager.mongodb_service.get_queue_items(
            order_id=order_id,
            kitchen_id=kitchen_id
        )
        
        if not order_items:
            return {
                "kitchen_id": kitchen_id,
                "order_id": order_id,
                "prep_time_minutes": 0,
                "items_count": 0,
                "estimated_completion": PrepTimeCalculator.get_ist_now().isoformat()
            }
        
        # Get current kitchen load
        current_load = await queue_manager.get_kitchen_current_load(kitchen_id)
        
        # Calculate kitchen-specific prep time
        prep_times = [item.get('prep_time_minutes', 10) for item in order_items]
        kitchen_prep_time = PrepTimeCalculator._calculate_kitchen_prep_time(prep_times, current_load)
        
        return {
            "kitchen_id": kitchen_id,
            "order_id": order_id,
            "prep_time_minutes": kitchen_prep_time,
            "items_count": len(order_items),
            "estimated_completion": (PrepTimeCalculator.get_ist_now() + timedelta(minutes=kitchen_prep_time)).isoformat(),
            "current_time_ist": PrepTimeCalculator.get_ist_now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Kitchen order prep time error: {str(e)}")

@router.get("/kitchen/{kitchen_id}/detailed")
async def get_detailed_kitchen_prep_time(kitchen_id: str):
    """Get detailed preparation time breakdown for a specific kitchen"""
    try:
        queue_manager = KitchenQueueManager()
        
        # Get kitchen queue items
        queue_items = await queue_manager.mongodb_service.get_queue_items(kitchen_id=kitchen_id)
        active_items = [item for item in queue_items if item.get('status') in ['queued', 'in_progress']]
        
        if not active_items:
            return {
                "kitchen_id": kitchen_id,
                "total_prep_time": 0,
                "batch_breakdown": [],
                "current_time_ist": PrepTimeCalculator.get_ist_now().isoformat()
            }
        
        # Calculate with current active load
        current_load = len([item for item in active_items if item.get('status') == 'in_progress'])
        prep_times = [item.get('prep_time_minutes', 10) for item in active_items if item.get('status') == 'queued']
        
        kitchen_prep_time = PrepTimeCalculator._calculate_kitchen_prep_time(prep_times, current_load)
        
        return {
            "kitchen_id": kitchen_id,
            "total_prep_time": kitchen_prep_time,
            "active_items": len(active_items),
            "queued_items": len(prep_times),
            "current_load": current_load,
            "estimated_completion": (PrepTimeCalculator.get_ist_now() + timedelta(minutes=kitchen_prep_time)).isoformat(),
            "current_time_ist": PrepTimeCalculator.get_ist_now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Detailed kitchen prep time error: {str(e)}")

@router.get("/kitchen/{kitchen_id}/manager-view", response_model=KitchenPrepTimeResponse)
async def get_kitchen_manager_prep_time(kitchen_id: str, order_id: Optional[str] = None):
    """Get kitchen-specific prep time for manager view (no buffer applied)"""
    try:
        queue_manager = KitchenQueueManager()
        
        if order_id:
            # Get specific order items for this kitchen
            order_items = await queue_manager.mongodb_service.get_queue_items(
                order_id=order_id,
                kitchen_id=kitchen_id
            )
        else:
            # Get all active items for this kitchen
            order_items = await queue_manager.mongodb_service.get_queue_items(
                kitchen_id=kitchen_id
            )
            order_items = [item for item in order_items if item.get('status') in ['queued', 'in_progress']]
        
        # Get current kitchen load
        current_load = await queue_manager.get_kitchen_current_load(kitchen_id)
        
        result = PrepTimeCalculator.get_kitchen_specific_prep_time(
            kitchen_id,
            order_items,
            {kitchen_id: current_load}
        )
        
        return KitchenPrepTimeResponse(
            kitchen_id=result['kitchen_id'],
            prep_time=result['prep_time'],
            estimated_completion=result['estimated_completion'].isoformat(),
            estimated_completion_ist=PrepTimeCalculator.format_ist_time(result['estimated_completion']),
            current_time_ist=PrepTimeCalculator.format_ist_time(PrepTimeCalculator.get_utc_now()),
            items_count=result['items_count']
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Kitchen manager view error: {str(e)}")

@router.post("/validate-scenarios")
async def validate_prep_time_scenarios():
    """Validate preparation time calculation against test scenarios"""
    try:
        scenarios = []
        
        # Scenario 1: Single Kitchen, Available Slots
        scenario1_items = [
            {"kitchen_id": "kitchen_1", "prep_time": 10, "item_id": "coffee"},
            {"kitchen_id": "kitchen_1", "prep_time": 5, "item_id": "juice"}
        ]
        result1 = PrepTimeCalculator.calculate_order_prep_time(scenario1_items, {"kitchen_1": 0})
        scenarios.append({
            "name": "Single Kitchen, Available Slots",
            "expected_time": 10,
            "actual_time": result1['kitchen_prep_times']['kitchen_1'],
            "passed": result1['kitchen_prep_times']['kitchen_1'] == 10
        })
        
        # Scenario 2: Single Kitchen, Slot Queueing (6th item)
        scenario2_items = [{"kitchen_id": "kitchen_1", "prep_time": 10, "item_id": "coffee"}]
        result2 = PrepTimeCalculator.calculate_order_prep_time(scenario2_items, {"kitchen_1": 5})
        scenarios.append({
            "name": "Single Kitchen, Slot Queueing",
            "expected_time_range": [18, 20],  # 8-10 min wait + 10 min prep
            "actual_time": result2['kitchen_prep_times']['kitchen_1'],
            "passed": 18 <= result2['kitchen_prep_times']['kitchen_1'] <= 20
        })
        
        # Scenario 3: Multi-Kitchen Order
        scenario3_items = [
            {"kitchen_id": "kitchen_1", "prep_time": 10, "item_id": "coffee"},
            {"kitchen_id": "kitchen_3", "prep_time": 20, "item_id": "pizza"}
        ]
        result3 = PrepTimeCalculator.calculate_order_prep_time(scenario3_items, {"kitchen_1": 0, "kitchen_3": 0})
        scenarios.append({
            "name": "Multi-Kitchen Order",
            "expected_time": 22,  # max(10, 20) + 2 buffer
            "actual_time": result3['total_prep_time'],
            "passed": result3['total_prep_time'] == 22
        })
        
        return {
            "validation_results": scenarios,
            "all_passed": all(s['passed'] for s in scenarios),
            "timestamp_ist": PrepTimeCalculator.format_ist_time(PrepTimeCalculator.get_utc_now())
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Validation error: {str(e)}")

