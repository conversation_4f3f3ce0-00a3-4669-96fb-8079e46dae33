
import React, { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Grid, List, Clock, Users, Plus, Settings, ShoppingCart, Filter, RefreshCw, MoreVertical, ChevronLeft, AlertCircle, Loader2 } from "lucide-react";
import { Link } from "react-router-dom";
import {
  useActiveOrders,
  useKitchens,
  useUpdateOrder,
  useUpdateOrderStatus,
  useAcceptOrder,
  useMarkOrderReady,
  useCompleteOrder,
  useHoldOrder,
  useResumeOrder
} from "@/hooks";
import { OrderStatus, OrderType } from "@/types/api";
import { PrepTimeCalculator, PrepTimeItem, KitchenSlotInfo } from "@/utils/prepTimeCalculator";

const KDS = () => {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [isManager, setIsManager] = useState(false);
  const [selectedKitchen, setSelectedKitchen] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState("all");
  const [currentTime, setCurrentTime] = useState(new Date());
  const [newKitchenName, setNewKitchenName] = useState("");
  const [isAddKitchenOpen, setIsAddKitchenOpen] = useState(false);

  // Backend integration
  const { data: orders, isLoading: ordersLoading, isError: ordersError, refetch: refetchOrders } = useActiveOrders();
  const { data: kitchensData, isLoading: kitchensLoading, isError: kitchensError, refetch: refetchKitchens } = useKitchens();

  // Order mutations
  const acceptOrderMutation = useAcceptOrder();
  const markReadyMutation = useMarkOrderReady();
  const completeOrderMutation = useCompleteOrder();
  const holdOrderMutation = useHoldOrder();
  const resumeOrderMutation = useResumeOrder();
  const updateOrderStatusMutation = useUpdateOrderStatus();
  
  // Process backend data
  const kitchens = kitchensData?.kitchens || [];
  const allOrders = orders || [];

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(timer);
  }, []);

  const handleAddKitchen = () => {
    if (newKitchenName.trim()) {
      // In a real implementation, this would call a backend API
      console.log("Add kitchen:", newKitchenName.trim());
      setNewKitchenName("");
      setIsAddKitchenOpen(false);
    }
  };

  const handleStatusChange = async (orderId: string, newStatus: OrderStatus) => {
    try {
      // Use the new direct status update API
      await updateOrderStatusMutation.mutateAsync({ orderId, status: newStatus });
    } catch (error) {
      console.error("Failed to update order status:", error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "bg-yellow-500 hover:bg-yellow-600";
      case "accepted": return "bg-blue-500 hover:bg-blue-600";
      case "preparing": return "bg-orange-500 hover:bg-orange-600";
      case "cooking": return "bg-purple-500 hover:bg-purple-600";
      case "ready": return "bg-green-500 hover:bg-green-600";
      case "hold": return "bg-red-500 hover:bg-red-600";
      default: return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "border-l-red-500 bg-red-50";
      case "medium": return "border-l-yellow-500 bg-yellow-50";
      case "low": return "border-l-green-500 bg-green-50";
      default: return "border-l-gray-500 bg-gray-50";
    }
  };

  const getKitchenStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-500";
      case "maintenance": return "bg-red-500";
      case "offline": return "bg-gray-500";
      default: return "bg-gray-500";
    }
  };

  // Enhanced preparation time calculator with proper slot management
  const calculateEnhancedPrepTime = (order: any, selectedKitchen?: string | null, allOrders: any[] = []) => {
    if (!order.queue_items || order.queue_items.length === 0) {
      return { 
        totalTime: 0, 
        kitchenTime: 0, 
        formattedTotal: "0m", 
        formattedKitchen: "0m",
        completionTimeIST: "N/A",
        bufferApplied: false
      };
    }

    const MAX_PARALLEL_ITEMS = 5;
    const ORDER_BUFFER_MINUTES = 2;
    
    // Get order placement time
    const orderPlacedTime = new Date(order.queue_items?.[0]?.scheduled_start || order.timestamp);
    
    // Get all orders placed before this order for FCFS slot calculation
    const earlierOrders = allOrders.filter(o => {
      const otherOrderTime = new Date(o.queue_items?.[0]?.scheduled_start || o.timestamp);
      return otherOrderTime < orderPlacedTime && o.order_id !== order.order_id;
    });

    // Calculate kitchen slot occupancy from earlier orders
    const getKitchenSlotOccupancy = (kitchenId: string, atTime: Date) => {
      let occupiedSlots = 0;
      const slotCompletionTimes: Date[] = [];
      
      earlierOrders.forEach(earlierOrder => {
        const earlierOrderTime = new Date(earlierOrder.queue_items?.[0]?.scheduled_start || earlierOrder.timestamp);
        const kitchenItems = earlierOrder.queue_items?.filter((qi: any) => qi.kitchen_id === kitchenId) || [];
        
        if (kitchenItems.length > 0) {
          // Calculate when this earlier order's items complete
          const prepTimes = kitchenItems.map((qi: any) => qi.prep_time || qi.prep_time_minutes || 10);
          const maxPrepTime = Math.max(...prepTimes);
          const completionTime = new Date(earlierOrderTime.getTime() + maxPrepTime * 60000);
          
          // If completion time is after current order time, slots are occupied
          if (completionTime > atTime) {
            const slotsUsed = Math.min(kitchenItems.length, MAX_PARALLEL_ITEMS);
            occupiedSlots += slotsUsed;
            
            // Track when slots become free
            for (let i = 0; i < slotsUsed; i++) {
              slotCompletionTimes.push(completionTime);
            }
          }
        }
      });
      
      return {
        occupiedSlots: Math.min(occupiedSlots, MAX_PARALLEL_ITEMS),
        availableSlots: Math.max(0, MAX_PARALLEL_ITEMS - occupiedSlots),
        earliestFreeSlot: slotCompletionTimes.length > 0 ? new Date(Math.min(...slotCompletionTimes.map(d => d.getTime()))) : atTime
      };
    };

    // Group items by kitchen
    const kitchenItems: Record<string, any[]> = {};
    order.queue_items.forEach((item: any) => {
      const kitchenId = item.kitchen_id || 'UNKNOWN';
      if (!kitchenItems[kitchenId]) {
        kitchenItems[kitchenId] = [];
      }
      kitchenItems[kitchenId].push(item);
    });

    const kitchenPrepTimes: Record<string, number> = {};
    const kitchenCompletionTimes: Record<string, Date> = {};

    // Calculate prep time for each kitchen
    Object.entries(kitchenItems).forEach(([kitchenId, items]) => {
      const slotInfo = getKitchenSlotOccupancy(kitchenId, orderPlacedTime);
      const prepTimes = items.map(item => item.prep_time || item.prep_time_minutes || 10);
      
      let kitchenStartTime = orderPlacedTime;
      let totalKitchenTime = 0;

      if (items.length === 1) {
        // Single item logic
        const prepTime = prepTimes[0];
        
        if (slotInfo.availableSlots > 0) {
          // Slot available, starts immediately
          totalKitchenTime = prepTime;
        } else {
          // Wait for earliest free slot
          const waitTime = Math.max(0, Math.floor((slotInfo.earliestFreeSlot.getTime() - orderPlacedTime.getTime()) / 60000));
          totalKitchenTime = waitTime + prepTime;
          kitchenStartTime = slotInfo.earliestFreeSlot;
        }
      } else {
        // Multi-item logic
        const sortedPrepTimes = [...prepTimes].sort((a, b) => b - a); // Longest first for optimal batching
        
        if (items.length <= slotInfo.availableSlots) {
          // All items fit in available slots - parallel processing
          totalKitchenTime = Math.max(...prepTimes);
        } else {
          // Need to batch items
          let currentTime = orderPlacedTime;
          
          // If no slots available, wait for earliest free slot
          if (slotInfo.availableSlots === 0) {
            currentTime = slotInfo.earliestFreeSlot;
          }
          
          // Process items in batches of MAX_PARALLEL_ITEMS
          for (let i = 0; i < sortedPrepTimes.length; i += MAX_PARALLEL_ITEMS) {
            const batchPrepTimes = sortedPrepTimes.slice(i, i + MAX_PARALLEL_ITEMS);
            const batchDuration = Math.max(...batchPrepTimes);
            currentTime = new Date(currentTime.getTime() + batchDuration * 60000);
          }
          
          totalKitchenTime = Math.floor((currentTime.getTime() - orderPlacedTime.getTime()) / 60000);
        }
      }

      kitchenPrepTimes[kitchenId] = totalKitchenTime;
      kitchenCompletionTimes[kitchenId] = new Date(orderPlacedTime.getTime() + totalKitchenTime * 60000);
    });

    if (selectedKitchen) {
      // Manager view: Show kitchen-specific time (no buffer)
      const kitchen = kitchens.find(k => k.name === selectedKitchen);
      if (kitchen && kitchenPrepTimes[kitchen.kitchen_id]) {
        const kitchenTime = kitchenPrepTimes[kitchen.kitchen_id];
        const completionTime = kitchenCompletionTimes[kitchen.kitchen_id];
        const completionTimeIST = completionTime.toLocaleTimeString('en-IN', {
          timeZone: 'Asia/Kolkata',
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        });
        
        return {
          totalTime: kitchenTime,
          kitchenTime: kitchenTime,
          formattedTotal: `${kitchenTime}m`,
          formattedKitchen: `${kitchenTime}m`,
          completionTimeIST: completionTimeIST,
          bufferApplied: false
        };
      }
    }

    // Overall order view: Show total time with buffer
    const maxKitchenTime = Math.max(...Object.values(kitchenPrepTimes));
    const totalWithBuffer = maxKitchenTime + ORDER_BUFFER_MINUTES;
    const completionTime = new Date(orderPlacedTime.getTime() + totalWithBuffer * 60000);
    const completionTimeIST = completionTime.toLocaleTimeString('en-IN', {
      timeZone: 'Asia/Kolkata',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
    
    return {
      totalTime: totalWithBuffer,
      kitchenTime: maxKitchenTime,
      formattedTotal: `${totalWithBuffer}m`,
      formattedKitchen: `${maxKitchenTime}m`,
      completionTimeIST: completionTimeIST,
      bufferApplied: true
    };
  };



  const formatTime = (minutes: number): string => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  };



  const filteredOrders = allOrders.filter(order => {
    if (filterStatus === "all") return true;
    return order.status === filterStatus;
  });

  console.log("Filtered orders:", filteredOrders);

  // Enhanced FCFS sorting with proper slot management
  const kitchenOrders = useMemo(() => {
    const filtered = selectedKitchen
      ? filteredOrders.filter(order => {
          const kitchen = kitchens.find(k => k.name === selectedKitchen);
          return kitchen && order.queue_items?.some(item => item.kitchen_id === kitchen.kitchen_id);
        })
      : filteredOrders;

    // Sort by order timestamp for proper FCFS (First-Come-First-Serve)
    // return filtered.sort((a, b) => {
    //   const timeA = new Date(a.queue_items?.[0]?.scheduled_start || a.timestamp || 0).getTime();
    //   const timeB = new Date(b.queue_items?.[0]?.scheduled_start || b.timestamp || 0).getTime();
    //   return timeA - timeB; // Earliest orders first
    // });
     return filtered;
  }, [selectedKitchen, filteredOrders, kitchens]);

  const OrderCard = ({ order, selectedKitchen, allOrders }: { order: any; selectedKitchen?: string | null; allOrders: any[] }) => {
    const priority = order.queue_items?.[0]?.priority || "medium";
    
    // Properly convert UTC timestamp to IST
    const orderTime = (() => {
      const orderTimestamp = order.queue_items?.[0]?.scheduled_start || order.timestamp;
      if (!orderTimestamp) return "N/A";
      
      const utcDate = new Date(orderTimestamp);
      const istTime = utcDate.toLocaleTimeString('en-IN', { 
        timeZone: 'Asia/Kolkata',
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true
      });
      
      return istTime;
    })();

    // Filter items and queue_items based on selected kitchen
    let displayItems = order.items || [];
    let displayQueueItems = order.queue_items || [];

    if (selectedKitchen) {
      const kitchen = kitchens.find(k => k.name === selectedKitchen);
      if (kitchen) {
        displayQueueItems = order.queue_items?.filter(qi => 
          qi.kitchen_id === kitchen.kitchen_id
        ) || [];
        
        const kitchenItemIds = displayQueueItems.map(qi => qi.item_id);
        displayItems = order.items?.filter(item => 
          kitchenItemIds.includes(item.item_id)
        ) || [];
      }
    }

    // Calculate enhanced prep time with proper slot management
    const prepTimeInfo = useMemo(() => {
      return calculateEnhancedPrepTime(order, selectedKitchen, allOrders);
    }, [order.order_id, selectedKitchen, allOrders.length]);

    // Show different information based on view mode
    const displayInfo = selectedKitchen 
      ? {
          timeLabel: "Kitchen Ready",
          time: prepTimeInfo.completionTimeIST,
          showBuffer: false,
          prepTime: prepTimeInfo.kitchenTime
        }
      : {
          timeLabel: "Estimated Time",
          time: prepTimeInfo.completionTimeIST,
          showBuffer: prepTimeInfo.bufferApplied,
          prepTime: prepTimeInfo.totalTime
        };

    return (
      <Card className={`mb-4 border-l-4 ${getPriorityColor(priority)} hover:shadow-lg transition-all duration-200`}>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg flex items-center gap-2">
                {order.order_id}
                {priority === "high" && <AlertCircle className="w-4 h-4 text-red-500" />}
                {selectedKitchen && (
                  <Badge variant="outline" className="text-xs">
                    {selectedKitchen} Items
                  </Badge>
                )}
              </CardTitle>
              <div className="text-sm text-gray-600 mt-1">
                {order.customer_name && (
                  <>
                    <span className="font-medium">
                      {order.table_number ? `Table ${order.table_number}` : 'Customer'}
                    </span> • {order.customer_name}
                  </>
                )}
              </div>
              <div className="text-xs text-gray-500">Ordered at {orderTime} </div>
            </div>
            <Badge className={`${getStatusColor(order.status)} text-white`}>
              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {displayQueueItems.length > 0 ? (
              displayQueueItems.map((queueItem: any, index: number) => {
                const matchingItem = displayItems.find(item => item.item_id === queueItem.item_id);
                return (
                  <div key={index} className="flex items-center gap-3 p-2 bg-white rounded-lg">
                    <div className="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                      <span className="text-xs font-medium">{queueItem.item_id.slice(-3)}</span>
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{queueItem.item_name}</p>
                      <p className="text-sm text-gray-600">
                        Prep: {queueItem.prep_time || queueItem.prep_time_minutes || 10}m • 
                        Order-Type: {order.order_type === 'DINE_IN' ? 'Dine In' : 
                                   order.order_type === 'TAKE_AWAY' ? 'Take Away' : 'Online Order'}
                      </p>
                      <p className="text-sm text-gray-600">
                        Qty: {matchingItem?.quantity || 1}
                      </p>
                      {selectedKitchen && (
                        <p className="text-sm text-blue-600">
                          Kitchen Prep: {queueItem.prep_time || 0} min
                        </p>
                      )}
                    </div>
                    {selectedKitchen && queueItem.status && (
                      <Badge variant="outline" className="text-xs">
                        {queueItem.status}
                      </Badge>
                    )}
                  </div>
                );
              })
            ) : (
              <div className="text-sm text-gray-500">No items found</div>
            )}
          
            <div className="flex gap-2 pt-4">
              {order.status === OrderStatus.PENDING && (
                <>
                  <Button
                    onClick={() => handleStatusChange(order.order_id, OrderStatus.ACCEPTED)}
                    size="sm"
                    className="bg-blue-500 hover:bg-blue-600"
                    disabled={acceptOrderMutation.isPending}
                  >
                    {acceptOrderMutation.isPending ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Accept'}
                  </Button>
                  <Button
                    onClick={() => handleStatusChange(order.order_id, OrderStatus.HOLD)}
                    variant="destructive"
                    size="sm"
                    disabled={holdOrderMutation.isPending}
                  >
                    {holdOrderMutation.isPending ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Hold'}
                  </Button>
                </>
              )}

              {(order.status === OrderStatus.ACCEPTED || order.status === OrderStatus.PREPARING || order.status === OrderStatus.COOKING) && (
                <select
                  className="px-3 py-1 border rounded-md bg-white"
                  value={order.status}
                  onChange={(e) => handleStatusChange(order.order_id, e.target.value as OrderStatus)}
                >
                  <option value={OrderStatus.PREPARING}>Preparing</option>
                  <option value={OrderStatus.COOKING}>Cooking</option>
                  <option value={OrderStatus.READY}>Ready for Delivery</option>
                  <option value={OrderStatus.HOLD}>Hold</option>
                </select>
              )}

              {order.status === OrderStatus.READY && (
                <Button
                  onClick={() => handleStatusChange(order.order_id, OrderStatus.COMPLETED)}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                  disabled={completeOrderMutation.isPending}
                >
                  {completeOrderMutation.isPending ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Mark Delivered'}
                </Button>
              )}

              {order.status === OrderStatus.HOLD && (
                <Button
                  onClick={() => handleStatusChange(order.order_id, OrderStatus.PREPARING)}
                  size="sm"
                  className="bg-orange-500 hover:bg-orange-600"
                  disabled={resumeOrderMutation.isPending}
                >
                  {resumeOrderMutation.isPending ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Resume'}
                </Button>
              )}
            </div>
          
            <div className="flex items-center justify-between pt-2 text-sm text-gray-600">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span className="font-medium text-green-600">
                    {displayInfo.timeLabel}: {displayInfo.time} 
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  {selectedKitchen ? displayQueueItems.length : (order.queue_items?.length || 0)} items
                </div>
                {selectedKitchen && (
                  <div className="text-xs text-blue-600">
                    Prep Time: {displayInfo.prepTime}m
                  </div>
                )}
                {!selectedKitchen && displayInfo.showBuffer && (
                  <div className="text-xs text-gray-500">
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const KitchenCard = ({ kitchen }: { kitchen: any }) => {
    const activeOrders = allOrders.filter(order =>
      order.queue_items?.some((item: any) => item.kitchen_id === kitchen.kitchen_id)
    ).length;

    return (
      <Card className="mb-4 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => setSelectedKitchen(kitchen.name)}>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg">{kitchen.name}</CardTitle>
            <Badge className={getKitchenStatusColor(kitchen.status || 'active')}>
              {(kitchen.status || 'active').charAt(0).toUpperCase() + (kitchen.status || 'active').slice(1)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <Users className="w-4 h-4" />
              {activeOrders} active orders
            </div>
            <div className="flex items-center gap-1">
              <Users className="w-4 h-4" />
              Load: {kitchen.current_load}/{kitchen.capacity}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Show loading state
  if (ordersLoading || kitchensLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto p-4 lg:p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading kitchen data...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (ordersError || kitchensError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto p-4 lg:p-6">
          <Alert variant="destructive" className="max-w-md mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load kitchen data. Please check your connection to the backend.
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  refetchOrders();
                  refetchKitchens();
                }}
                className="ml-2"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto p-4 lg:p-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold">Kitchen Display System</h1>
            <p className="text-gray-600 mt-1">
              {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </p>
            <p className="text-sm text-gray-500">
              {allOrders.length} total orders • {kitchens.length} kitchens
            </p>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {selectedKitchen && (
              <Button
                variant="outline"
                onClick={() => setSelectedKitchen(null)}
                size="sm"
                className="flex items-center gap-2"
              >
                <ChevronLeft className="w-4 h-4" />
                Back to All
              </Button>
            )}
            
            <Button
              variant={isManager ? "default" : "outline"}
              onClick={() => setIsManager(!isManager)}
              size="sm"
              className="flex items-center gap-2"
            >
              <Settings className="w-4 h-4" />
              Manager Mode
            </Button>
            
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              onClick={() => setViewMode("grid")}
              size="sm"
            >
              <Grid className="w-4 h-4" />
            </Button>
            
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              onClick={() => setViewMode("list")}
              size="sm"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Manager Section */}
        {isManager && !selectedKitchen && (
          <div className="mb-8">
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4 gap-4">
              <h2 className="text-xl lg:text-2xl font-semibold">Kitchen Management</h2>
              <Dialog open={isAddKitchenOpen} onOpenChange={setIsAddKitchenOpen}>
                <DialogTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <Plus className="w-4 h-4" />
                    Add Kitchen
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add New Kitchen</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="kitchen-name">Kitchen Name</Label>
                      <Input
                        id="kitchen-name"
                        value={newKitchenName}
                        onChange={(e) => setNewKitchenName(e.target.value)}
                        placeholder="Enter kitchen name"
                      />
                    </div>
                    <Button onClick={handleAddKitchen} className="w-full">
                      Add Kitchen
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
            
            <div className={viewMode === "grid" ? "grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-8" : "space-y-4 mb-8"}>
              {kitchens.map((kitchen) => (
                <KitchenCard key={kitchen.kitchen_id} kitchen={kitchen} />
              ))}
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="flex flex-wrap items-center gap-4 mb-6">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4" />
            <span className="text-sm font-medium">Filter by status:</span>
          </div>
          <select 
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-1 border rounded-md bg-white text-sm"
          >
            <option value="all">All Orders</option>
            <option value="pending">Pending</option>
            <option value="preparing">Preparing</option>
            <option value="cooking">Cooking</option>
            <option value="ready">Ready</option>
            <option value="hold">On Hold</option>
          </select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              refetchOrders();
              refetchKitchens();
            }}
            className="flex items-center gap-2"
            disabled={ordersLoading || kitchensLoading}
          >
            <RefreshCw className={`w-4 h-4 ${ordersLoading || kitchensLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Orders Section */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl lg:text-2xl font-semibold">
            {selectedKitchen ? `${selectedKitchen} - Kitchen Items` : 'Active Orders'}
          </h2>
          <div className="text-sm text-gray-600">
            {selectedKitchen 
              ? `${kitchenOrders.reduce((total, order) => {
                  const kitchen = kitchens.find(k => k.name === selectedKitchen);
                  const kitchenItems = kitchen ? order.queue_items?.filter(qi => qi.kitchen_id === kitchen.kitchen_id) || [] : [];
                  return total + kitchenItems.length;
                }, 0)} kitchen items from ${kitchenOrders.length} orders`
              : `${kitchenOrders.length} orders`
            }
          </div>
        </div>
        
        <div className={viewMode === "grid" ? "grid sm:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4" : "space-y-4"}>
          {kitchenOrders.map((order) => (
            <OrderCard 
              key={order.order_id} 
              order={order} 
              selectedKitchen={selectedKitchen}
              allOrders={kitchenOrders}
            />
          ))}
        </div>

        {kitchenOrders.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No orders found for the selected filters.</p>
          </div>
        )}
      </div>

      {/* Floating Order Now Button */}
      <Link
        to="/menu"
        className="fixed bottom-6 left-6 bg-primary hover:bg-primary/90 text-primary-foreground p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 z-50"
        title="Order Now"
      >
        <ShoppingCart className="w-6 h-6" />
      </Link>
    </div>
  );
};


export default KDS;








