import dotenv from 'dotenv';
import { AppConfig, DatabaseConfig, RedisConfig, AIServiceConfig, QueueConfig, KitchenCapacityConfig } from '@/types';

// Load environment variables
dotenv.config();

// Validate required environment variables
const requiredEnvVars = [
  'DATABASE_URL',
  'REDIS_URL',
  'AI_SERVICE_URL',
  'JWT_SECRET'
];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

// Application Configuration
export const appConfig: AppConfig = {
  port: parseInt(process.env.PORT || '3000', 10),
  nodeEnv: process.env.NODE_ENV || 'development',
  apiPrefix: process.env.API_PREFIX || '/api/v1',
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  jwtSecret: process.env.JWT_SECRET!,
  logLevel: process.env.LOG_LEVEL || 'info',
  logFile: process.env.LOG_FILE || 'logs/kds.log'
};

// Database Configuration
export const databaseConfig: DatabaseConfig = {
  url: process.env.DATABASE_URL!
};

// Redis Configuration
export const redisConfig: RedisConfig = {
  url: process.env.REDIS_URL!,
  password: process.env.REDIS_PASSWORD || undefined,
  ttl: parseInt(process.env.REDIS_TTL_SECONDS || '300', 10)
};

// AI Service Configuration
export const aiServiceConfig: AIServiceConfig = {
  url: process.env.AI_SERVICE_URL!,
  timeout: parseInt(process.env.AI_SERVICE_TIMEOUT || '30000', 10)
};

// Queue Management Configuration
export const queueConfig: QueueConfig = {
  maxQueueSize: parseInt(process.env.MAX_QUEUE_SIZE || '50', 10),
  sequenceInterval: parseInt(process.env.SEQUENCE_INTERVAL_SECONDS || '10', 10),
  aiTriggerThreshold: parseInt(process.env.AI_TRIGGER_THRESHOLD || '3', 10),
  starvationThreshold: parseInt(process.env.STARVATION_THRESHOLD_MINUTES || '15', 10)
};

// Kitchen Capacity Configuration
export const kitchenCapacityConfig: KitchenCapacityConfig = {
  grill: parseInt(process.env.GRILL_CAPACITY || '3', 10),
  fryer: parseInt(process.env.FRYER_CAPACITY || '2', 10),
  cold: parseInt(process.env.COLD_CAPACITY || '4', 10),
  bar: parseInt(process.env.BAR_CAPACITY || '2', 10)
};

// Rate Limiting Configuration
export const rateLimitConfig = {
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
  maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10)
};

// WebSocket Configuration
export const websocketConfig = {
  pingInterval: parseInt(process.env.WEBSOCKET_PING_INTERVAL || '25000', 10),
  pingTimeout: parseInt(process.env.WEBSOCKET_PING_TIMEOUT || '5000', 10)
};

// Performance Configuration
export const performanceConfig = {
  historyDays: parseInt(process.env.PERFORMANCE_HISTORY_DAYS || '30', 10)
};

// Validation function
export const validateConfig = (): boolean => {
  try {
    // Validate port range
    if (appConfig.port < 1 || appConfig.port > 65535) {
      throw new Error('Port must be between 1 and 65535');
    }

    // Validate URLs
    new URL(redisConfig.url);
    new URL(aiServiceConfig.url);

    // Validate positive integers
    if (queueConfig.maxQueueSize <= 0) {
      throw new Error('MAX_QUEUE_SIZE must be positive');
    }

    if (queueConfig.sequenceInterval <= 0) {
      throw new Error('SEQUENCE_INTERVAL_SECONDS must be positive');
    }

    if (aiServiceConfig.timeout <= 0) {
      throw new Error('AI_SERVICE_TIMEOUT must be positive');
    }

    // Validate kitchen capacities
    const capacities = Object.values(kitchenCapacityConfig);
    if (capacities.some(capacity => capacity <= 0)) {
      throw new Error('All kitchen capacities must be positive');
    }

    return true;
  } catch (error) {
    console.error('Configuration validation failed:', error);
    return false;
  }
};

// Export all configurations
export const config = {
  app: appConfig,
  database: databaseConfig,
  redis: redisConfig,
  aiService: aiServiceConfig,
  queue: queueConfig,
  kitchenCapacity: kitchenCapacityConfig,
  rateLimit: rateLimitConfig,
  websocket: websocketConfig,
  performance: performanceConfig,
  validate: validateConfig
};

export default config;
