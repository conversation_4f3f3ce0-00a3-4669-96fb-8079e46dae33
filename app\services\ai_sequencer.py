"""
AI-powered kitchen sequencing service.
"""

import logging
from typing import List, Dict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from app.database.models import Order, OrderItem, MenuItem, Kitchen
from app.services.ollama_client import ollama_client

logger = logging.getLogger(__name__)

class AISequencer:
    async def get_training_data(self, db: AsyncSession) -> str:
        """Get kitchen and menu data for training."""
        try:
            # Get kitchens with menu items
            result = await db.execute(
                select(Kitchen).options(selectinload(Kitchen.menu_items))
            )
            kitchens = result.scalars().all()
            
            training_data = "KITCHEN CONFIGURATION:\n"
            
            for kitchen in kitchens:
                training_data += f"\nKitchen: {kitchen.kitchen_name} (ID: {kitchen.kitchen_id})\n"
                training_data += f"Capacity: {kitchen.capacity}\n"
                training_data += f"Specialization: {kitchen.specialization}\n"
                training_data += "Menu Items:\n"
                
                for item in kitchen.menu_items:
                    training_data += f"- {item.item_name}: {item.prep_time_minutes}min, {item.difficulty_level}\n"
            
            return training_data
            
        except Exception as e:
            logger.error(f"Failed to get training data: {e}")
            return ""
    
    async def train_model(self, db: AsyncSession) -> bool:
        """Train AI model with kitchen data."""
        training_data = await self.get_training_data(db)
        if training_data:
            return await ollama_client.train_with_data(training_data)
        return False
    
    async def sequence_order(self, db: AsyncSession, order_id: str) -> Dict:
        """Generate AI sequence for order items."""
        try:
            # Get order with items
            result = await db.execute(
                select(Order)
                .options(selectinload(Order.items).selectinload(OrderItem.menu_item))
                .where(Order.order_id == order_id)
            )
            order = result.scalar_one_or_none()
            
            if not order:
                return {"error": "Order not found"}
            
            # Prepare order data for AI
            order_info = f"Order ID: {order.order_id}\n"
            order_info += f"Customer: {order.customer_name}\n"
            order_info += f"Table: {order.table_number}\n"
            order_info += "Items:\n"
            
            for item in order.items:
                menu_item = item.menu_item
                if menu_item:
                    order_info += f"- {item.item_name} x{item.quantity} (Kitchen: {menu_item.kitchen_id}, Prep: {menu_item.prep_time_minutes}min)\n"
            
            # AI prompt for sequencing
            prompt = f"""
            Based on the kitchen configuration you learned, sequence these order items for optimal preparation:
            
            {order_info}
            
            Provide a JSON response with:
            1. Kitchen assignments
            2. Preparation sequence
            3. Estimated completion times
            4. Any coordination notes
            
            Format: {{"sequence": [{{"item": "item_name", "kitchen": "kitchen_id", "start_time": "minutes", "duration": "minutes"}}], "notes": "coordination notes"}}
            """
            
            ai_response = await ollama_client.generate_sequence(prompt)
            
            return {
                "order_id": order_id,
                "ai_sequence": ai_response,
                "order_items": len(order.items)
            }
            
        except Exception as e:
            logger.error(f"Failed to sequence order: {e}")
            return {"error": str(e)}

ai_sequencer = AISequencer()