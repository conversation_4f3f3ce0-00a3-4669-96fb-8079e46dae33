"""
Test script for AI system.
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_ai_system():
    """Test the complete AI system."""
    
    print("🤖 Testing AI Kitchen Sequencing System")
    print("=" * 50)
    
    # 1. Seed database
    print("1. Seeding database from Excel...")
    response = requests.post(f"{BASE_URL}/api/ai/seed-database")
    if response.status_code == 200:
        print("✅ Database seeded successfully")
    else:
        print(f"❌ Failed to seed database: {response.text}")
        return
    
    # 2. Train AI model
    print("2. Training AI model...")
    response = requests.post(f"{BASE_URL}/api/ai/train")
    if response.status_code == 200:
        print("✅ AI model trained successfully")
    else:
        print(f"❌ Failed to train AI: {response.text}")
    
    # 3. Create test order
    print("3. Creating test order...")
    order_data = {
        "order_id": f"AI_TEST_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "customer_name": "AI Test Customer",
        "table_number": "10",
        "items": [
            {"item_id": "PIZZA_001", "item_name": "Margherita Pizza", "quantity": 1},
            {"item_id": "PASTA_001", "item_name": "Spaghetti Carbonara", "quantity": 1},
            {"item_id": "SALAD_001", "item_name": "Caesar Salad", "quantity": 2}
        ]
    }
    
    response = requests.post(f"{BASE_URL}/api/orders/", json=order_data)
    if response.status_code == 200:
        order = response.json()
        order_id = order["order_id"]
        print(f"✅ Test order created: {order_id}")
    else:
        print(f"❌ Failed to create order: {response.text}")
        return
    
    # 4. Get AI sequence
    print("4. Getting AI sequence...")
    response = requests.post(f"{BASE_URL}/api/ai/sequence/{order_id}")
    if response.status_code == 200:
        sequence = response.json()
        print("✅ AI sequence generated:")
        print(json.dumps(sequence, indent=2))
    else:
        print(f"❌ Failed to get AI sequence: {response.text}")
    
    # 5. Get training data
    print("5. Getting training data...")
    response = requests.get(f"{BASE_URL}/api/ai/training-data")
    if response.status_code == 200:
        data = response.json()
        print("✅ Training data retrieved")
        print(f"Data preview: {data['training_data'][:200]}...")
    else:
        print(f"❌ Failed to get training data: {response.text}")

if __name__ == "__main__":
    try:
        test_ai_system()
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed: {e}")