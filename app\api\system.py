"""
System API endpoints.
"""

from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.connection import get_db_session, db_manager

router = APIRouter()

@router.get("/health")
async def health_check(db: AsyncSession = Depends(get_db_session)):
    """System health check."""
    db_health = await db_manager.health_check()
    
    return {
        "status": "healthy",
        "database": db_health,
        "version": "1.0.0"
    }