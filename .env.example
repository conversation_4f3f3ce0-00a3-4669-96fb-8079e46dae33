# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/kds_queue_management"

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""

# Server Configuration
PORT=3000
NODE_ENV=development
API_PREFIX=/api/v1

# AI Microservice Configuration
AI_SERVICE_URL=http://localhost:8001
AI_SERVICE_TIMEOUT=30000

# Security
JWT_SECRET=your-super-secret-jwt-key-here
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/kds.log

# Queue Management Settings
MAX_QUEUE_SIZE=50
SEQUENCE_INTERVAL_SECONDS=10
REDIS_TTL_SECONDS=300

# Kitchen Capacity Limits
GRILL_CAPACITY=3
FRYER_CAPACITY=2
COLD_CAPACITY=4
BAR_CAPACITY=2

# Performance Settings
AI_TRIGGER_THRESHOLD=3
STARVATION_THRESHOLD_MINUTES=15
PERFORMANCE_HISTORY_DAYS=30

# WebSocket Settings
WEBSOCKET_PING_INTERVAL=25000
WEBSOCKET_PING_TIMEOUT=5000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
