#!/usr/bin/env python3
"""
Debug script to check data fetching from database and Excel
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.mongodb_service import mongodb_service
from app.services.excel_service import ExcelDataService

async def debug_data_fetch():
    """Debug data fetching from both MongoDB and Excel"""
    
    print("=" * 60)
    print("DEBUG: Data Fetching Analysis")
    print("=" * 60)
    
    # Initialize services
    excel_service = ExcelDataService()
    
    # Connect to MongoDB
    await mongodb_service.connect()
    
    print("\n1. MONGODB CONNECTION STATUS:")
    health = await mongodb_service.health_check()
    print(f"   Status: {health.get('status')}")
    print(f"   Database: {health.get('database')}")
    if 'collections' in health:
        for collection, count in health['collections'].items():
            print(f"   {collection}: {count} records")
    
    print("\n2. EXCEL DATA LOADING:")
    try:
        # Load menu items from Excel
        excel_menu_items = excel_service.load_menu_items()
        print(f"   Excel menu items loaded: {len(excel_menu_items)}")
        
        # Show first few items with prep times
        print("\n   Sample Excel menu items:")
        for i, (item_id, item_data) in enumerate(list(excel_menu_items.items())[:5]):
            prep_time = item_data.get('prep_time_minutes', item_data.get('prep_time', 'NOT_FOUND'))
            kitchen_id = item_data.get('kitchen_id', 'NOT_FOUND')
            print(f"   - {item_id}: prep_time={prep_time}, kitchen_id={kitchen_id}")
            print(f"     Full data: {item_data}")
        
        # Load kitchen configs
        kitchen_configs = excel_service.load_kitchen_configs()
        print(f"\n   Excel kitchen configs loaded: {len(kitchen_configs)}")
        for kitchen_id, config in kitchen_configs.items():
            print(f"   - {kitchen_id}: {config.get('kitchen_name', config.get('name'))}")
            
    except Exception as e:
        print(f"   ERROR loading Excel data: {e}")
    
    print("\n3. MONGODB DATA FETCHING:")
    try:
        # Get menu items from MongoDB
        mongo_menu_items = await mongodb_service.get_menu_items()
        print(f"   MongoDB menu items fetched: {len(mongo_menu_items)}")
        
        # Show first few items with prep times
        print("\n   Sample MongoDB menu items:")
        for item in mongo_menu_items[:5]:
            item_id = item.get('item_id', 'NO_ID')
            prep_time = item.get('prep_time_minutes', item.get('prep_time', 'NOT_FOUND'))
            kitchen_id = item.get('kitchen_id', 'NOT_FOUND')
            print(f"   - {item_id}: prep_time={prep_time}, kitchen_id={kitchen_id}")
            print(f"     Full data: {item}")
        
        # Get kitchens from MongoDB
        mongo_kitchens = await mongodb_service.get_kitchens()
        print(f"\n   MongoDB kitchens fetched: {len(mongo_kitchens)}")
        for kitchen in mongo_kitchens:
            kitchen_id = kitchen.get('kitchen_id', 'NO_ID')
            name = kitchen.get('kitchen_name', kitchen.get('name', 'NO_NAME'))
            print(f"   - {kitchen_id}: {name}")
            
    except Exception as e:
        print(f"   ERROR fetching MongoDB data: {e}")
    
    print("\n4. DATA SYNC CHECK:")
    try:
        # Check if Excel data is synced to MongoDB
        if excel_menu_items and len(mongo_menu_items) == 0:
            print("   WARNING: Excel has data but MongoDB is empty!")
            print("   Attempting to sync Excel data to MongoDB...")
            
            # Convert Excel data to MongoDB format
            items_to_sync = []
            for item_id, item_data in excel_menu_items.items():
                mongo_item = {
                    'item_id': item_id,
                    'item_name': item_data.get('item_name', item_data.get('name', item_id)),
                    'kitchen_id': item_data.get('kitchen_id'),
                    'prep_time_minutes': item_data.get('prep_time_minutes', item_data.get('prep_time', 10)),
                    'category': item_data.get('category', 'general'),
                    'difficulty_level': item_data.get('difficulty_level', 'medium'),
                    'available': item_data.get('available', True)
                }
                items_to_sync.append(mongo_item)
            
            # Sync to MongoDB
            success = await mongodb_service.upsert_menu_items(items_to_sync)
            if success:
                print(f"   SUCCESS: Synced {len(items_to_sync)} menu items to MongoDB")
            else:
                print("   ERROR: Failed to sync menu items to MongoDB")
                
            # Sync kitchens too
            kitchens_to_sync = []
            for kitchen_id, kitchen_data in kitchen_configs.items():
                mongo_kitchen = {
                    'kitchen_id': kitchen_id,
                    'kitchen_name': kitchen_data.get('kitchen_name', kitchen_data.get('name', kitchen_id)),
                    'capacity': kitchen_data.get('capacity', 1),
                    'specialization': kitchen_data.get('specialization', 'general'),
                    'status': kitchen_data.get('status', 'active')
                }
                kitchens_to_sync.append(mongo_kitchen)
            
            success = await mongodb_service.upsert_kitchens(kitchens_to_sync)
            if success:
                print(f"   SUCCESS: Synced {len(kitchens_to_sync)} kitchens to MongoDB")
            else:
                print("   ERROR: Failed to sync kitchens to MongoDB")
        
        elif len(excel_menu_items) > 0 and len(mongo_menu_items) > 0:
            print("   INFO: Both Excel and MongoDB have data")
            
            # Check for prep time issues
            prep_time_issues = []
            for mongo_item in mongo_menu_items:
                prep_time = mongo_item.get('prep_time_minutes', mongo_item.get('prep_time'))
                if prep_time is None or prep_time <= 2:
                    prep_time_issues.append({
                        'item_id': mongo_item.get('item_id'),
                        'current_prep_time': prep_time
                    })
            
            if prep_time_issues:
                print(f"\n   WARNING: Found {len(prep_time_issues)} items with low prep times (≤2 min):")
                for issue in prep_time_issues[:10]:  # Show first 10
                    print(f"   - {issue['item_id']}: {issue['current_prep_time']} minutes")
                
                print("\n   Fixing prep times...")
                # Fix prep times based on item categories or set reasonable defaults
                fixed_items = []
                for mongo_item in mongo_menu_items:
                    prep_time = mongo_item.get('prep_time_minutes', mongo_item.get('prep_time'))
                    if prep_time is None or prep_time <= 2:
                        # Set reasonable prep times based on category
                        category = mongo_item.get('category', 'general').lower()
                        if 'grill' in category or 'meat' in category:
                            new_prep_time = 15
                        elif 'pasta' in category or 'noodle' in category:
                            new_prep_time = 12
                        elif 'dessert' in category or 'sweet' in category:
                            new_prep_time = 8
                        elif 'salad' in category or 'cold' in category:
                            new_prep_time = 5
                        else:
                            new_prep_time = 10
                        
                        mongo_item['prep_time_minutes'] = new_prep_time
                        fixed_items.append(mongo_item)
                
                if fixed_items:
                    success = await mongodb_service.upsert_menu_items(fixed_items)
                    if success:
                        print(f"   SUCCESS: Fixed prep times for {len(fixed_items)} items")
                    else:
                        print("   ERROR: Failed to fix prep times")
            else:
                print("   INFO: All items have reasonable prep times")
        
    except Exception as e:
        print(f"   ERROR in data sync check: {e}")
    
    print("\n5. FINAL VERIFICATION:")
    try:
        # Re-fetch data to verify fixes
        final_menu_items = await mongodb_service.get_menu_items()
        final_kitchens = await mongodb_service.get_kitchens()
        
        print(f"   Final menu items count: {len(final_menu_items)}")
        print(f"   Final kitchens count: {len(final_kitchens)}")
        
        # Check prep time distribution
        prep_times = [item.get('prep_time_minutes', item.get('prep_time', 0)) for item in final_menu_items]
        if prep_times:
            avg_prep_time = sum(prep_times) / len(prep_times)
            min_prep_time = min(prep_times)
            max_prep_time = max(prep_times)
            print(f"   Prep time stats: avg={avg_prep_time:.1f}, min={min_prep_time}, max={max_prep_time}")
        
        # Check kitchen assignments
        items_without_kitchen = [item for item in final_menu_items if not item.get('kitchen_id')]
        if items_without_kitchen:
            print(f"   WARNING: {len(items_without_kitchen)} items without kitchen assignment")
        else:
            print("   INFO: All items have kitchen assignments")
            
    except Exception as e:
        print(f"   ERROR in final verification: {e}")
    
    print("\n" + "=" * 60)
    print("DEBUG COMPLETE")
    print("=" * 60)
    
    # Disconnect
    await mongodb_service.disconnect()

if __name__ == "__main__":
    asyncio.run(debug_data_fetch())