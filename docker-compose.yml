
services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: kds-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: smart_kitchen
    ports:
      - "27117:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - kds-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend API Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kds-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - MONGODB_URL=************************************************************************
      - MONGODB_DATABASE=smart_kitchen
      - OLLAMA_BASE_URL=http://************:11434
      - OLLAMA_MODEL=llama3.1:8b
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - DEBUG_MODE=true
      - MAX_STARVATION_COUNT=3
      - SYNCHRONIZATION_WINDOW_MINUTES=3
      - PERFORMANCE_HISTORY_DAYS=30
      - LOG_LEVEL=INFO
      - LOG_FILE=logs/kitchen_queue.log
      - EXCEL_FILE_PATH=data/kitchen_config.xlsx
    volumes:
      - ./app:/app/app:cached          # Mount source code for hot reload
      - ./data:/app/data:cached        # Mount data directory
      - ./logs:/app/logs:cached        # Mount logs directory
      - ./requirements.txt:/app/requirements.txt:ro  # Mount requirements (read-only)
    command: ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--reload-dir", "/app/app"]
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - kds-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Frontend Web Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=http://localhost:8000
    container_name: kds-frontend
    restart: unless-stopped
    ports:
      - "8180:80"
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
      - REACT_APP_API_BASE_URL=http://localhost:8000
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - kds-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

# Networks
networks:
  kds-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  mongodb_data:
    driver: local

