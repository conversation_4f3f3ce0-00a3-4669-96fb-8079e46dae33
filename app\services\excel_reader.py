"""
Excel reader for kitchen configuration data.
"""

import pandas as pd
import logging

logger = logging.getLogger(__name__)

class ExcelReader:
    def __init__(self, file_path: str):
        self.file_path = file_path
    
    def read_kitchen_config(self):
        """Read kitchen configuration from Excel."""
        try:
            # Read all sheets
            excel_data = pd.read_excel(self.file_path, sheet_name=None)
            
            result = {}
            for sheet_name, df in excel_data.items():
                result[sheet_name] = df.to_dict('records')
                logger.info(f"Read {len(df)} rows from sheet: {sheet_name}")
            
            return result
        except Exception as e:
            logger.error(f"Failed to read Excel file: {e}")
            raise

excel_reader = ExcelReader("kitchen_config.xlsx")