"""
Simplified models for order management only.
"""

from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.models.enums import OrderStatus, ItemStatus, OrderType

class OrderItem(BaseModel):
    """Order item model."""
    item_id: str = Field(..., description="Menu item identifier")
    item_name: str = Field(..., description="Item name")
    quantity: int = Field(..., gt=0, description="Quantity ordered")
    notes: Optional[str] = Field(None, description="Special instructions")

class Order(BaseModel):
    """Order model."""
    order_id: str = Field(..., description="Unique order identifier")
    customer_name: Optional[str] = Field(None, description="Customer name")
    table_number: Optional[str] = Field(None, description="Table number")
    order_type: OrderType = Field(OrderType.DINE_IN, description="Order type")
    items: List[OrderItem] = Field(..., min_items=1, description="Order items")
    status: OrderStatus = Field(OrderStatus.PENDING, description="Order status")
    notes: Optional[str] = Field(None, description="Order notes")
    created_at: datetime = Field(default_factory=datetime.now, description="Creation time")
    updated_at: Optional[datetime] = Field(None, description="Last update time")

class OrderStatusUpdate(BaseModel):
    """Order status update model."""
    status: OrderStatus = Field(..., description="New order status")
    notes: Optional[str] = Field(None, description="Update notes")