"""
Simplified order API endpoints.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from app.database.connection import get_db_session
from app.services.order_service import order_service
from app.models.base import Order, OrderStatusUpdate
from app.models.enums import OrderStatus

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/", response_model=Order)
async def create_order(
    order: Order,
    db: AsyncSession = Depends(get_db_session)
):
    """Create a new order."""
    try:
        return await order_service.create_order(db, order)
    except Exception as e:
        logger.error(f"Failed to create order: {e}")
        raise HTTPException(status_code=500, detail="Failed to create order")

@router.get("/{order_id}", response_model=Order)
async def get_order(
    order_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """Get order by ID."""
    order = await order_service.get_order(db, order_id)
    if not order:
        raise HTTPException(status_code=404, detail="Order not found")
    return order

@router.get("/", response_model=List[Order])
async def get_orders(
    status: Optional[str] = None,
    limit: int = 100,
    db: AsyncSession = Depends(get_db_session)
):
    """Get list of orders."""
    return await order_service.get_orders(db, status, limit)

@router.put("/{order_id}/status", response_model=Order)
async def update_order_status(
    order_id: str,
    status_update: OrderStatusUpdate,
    db: AsyncSession = Depends(get_db_session)
):
    """Update order status."""
    order = await order_service.update_order_status(db, order_id, status_update)
    if not order:
        raise HTTPException(status_code=404, detail="Order not found")
    return order

@router.put("/{order_id}/confirm", response_model=Order)
async def confirm_order(
    order_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """Confirm an order."""
    status_update = OrderStatusUpdate(status=OrderStatus.CONFIRMED)
    return await update_order_status(order_id, status_update, db)

@router.put("/{order_id}/start-preparing", response_model=Order)
async def start_preparing(
    order_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """Start preparing an order."""
    status_update = OrderStatusUpdate(status=OrderStatus.PREPARING)
    return await update_order_status(order_id, status_update, db)

@router.put("/{order_id}/ready", response_model=Order)
async def mark_ready(
    order_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """Mark order as ready."""
    status_update = OrderStatusUpdate(status=OrderStatus.READY)
    return await update_order_status(order_id, status_update, db)

@router.put("/{order_id}/deliver", response_model=Order)
async def deliver_order(
    order_id: str,
    db: AsyncSession = Depends(get_db_session)
):
    """Mark order as delivered."""
    status_update = OrderStatusUpdate(status=OrderStatus.DELIVERED)
    return await update_order_status(order_id, status_update, db)