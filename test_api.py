"""
Simple test script for the Kitchen Display System API.
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_order_flow():
    """Test the complete order flow."""
    
    # 1. Create an order
    order_data = {
        "order_id": f"ORDER_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "customer_name": "John Doe",
        "table_number": "5",
        "order_type": "dine_in",
        "items": [
            {
                "item_id": "PIZZA_001",
                "item_name": "Margherita Pizza",
                "quantity": 1,
                "notes": "Extra cheese"
            },
            {
                "item_id": "DRINK_001", 
                "item_name": "Coca Cola",
                "quantity": 2
            }
        ],
        "notes": "Customer allergic to nuts"
    }
    
    print("1. Creating order...")
    response = requests.post(f"{BASE_URL}/api/orders/", json=order_data)
    if response.status_code == 200:
        order = response.json()
        order_id = order["order_id"]
        print(f"✅ Order created: {order_id}")
    else:
        print(f"❌ Failed to create order: {response.text}")
        return
    
    # 2. Confirm order
    print("2. Confirming order...")
    response = requests.put(f"{BASE_URL}/api/orders/{order_id}/confirm")
    if response.status_code == 200:
        print("✅ Order confirmed")
    else:
        print(f"❌ Failed to confirm order: {response.text}")
    
    # 3. Start preparing
    print("3. Starting preparation...")
    response = requests.put(f"{BASE_URL}/api/orders/{order_id}/start-preparing")
    if response.status_code == 200:
        print("✅ Started preparing")
    else:
        print(f"❌ Failed to start preparing: {response.text}")
    
    # 4. Mark as ready
    print("4. Marking as ready...")
    response = requests.put(f"{BASE_URL}/api/orders/{order_id}/ready")
    if response.status_code == 200:
        print("✅ Order ready")
    else:
        print(f"❌ Failed to mark ready: {response.text}")
    
    # 5. Deliver order
    print("5. Delivering order...")
    response = requests.put(f"{BASE_URL}/api/orders/{order_id}/deliver")
    if response.status_code == 200:
        print("✅ Order delivered")
    else:
        print(f"❌ Failed to deliver order: {response.text}")
    
    # 6. Get final order status
    print("6. Getting final order status...")
    response = requests.get(f"{BASE_URL}/api/orders/{order_id}")
    if response.status_code == 200:
        order = response.json()
        print(f"✅ Final status: {order['status']}")
        print(f"Order details: {json.dumps(order, indent=2, default=str)}")
    else:
        print(f"❌ Failed to get order: {response.text}")

def test_health():
    """Test system health."""
    print("Testing system health...")
    response = requests.get(f"{BASE_URL}/api/system/health")
    if response.status_code == 200:
        health = response.json()
        print(f"✅ System healthy: {json.dumps(health, indent=2)}")
    else:
        print(f"❌ System unhealthy: {response.text}")

if __name__ == "__main__":
    print("🧪 Testing Kitchen Display System API")
    print("=" * 50)
    
    try:
        test_health()
        print()
        test_order_flow()
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Test failed: {e}")