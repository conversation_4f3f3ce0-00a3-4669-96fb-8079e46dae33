"""
Intelligent Preparation Time Calculator for Smart Kitchen Queue System
"""

import pytz
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from collections import defaultdict

# IST timezone
IST = pytz.timezone('Asia/Kolkata')

class PrepTimeCalculator:
    """Enhanced preparation time calculator with slot management and IST handling"""
    
    MAX_PARALLEL_ITEMS = 5
    ORDER_BUFFER_MINUTES = 2  # 2-minute buffer for final estimates
    
    @staticmethod
    def get_ist_now() -> datetime:
        """Get current time in IST"""
        return datetime.now(IST)
    
    @staticmethod
    def get_utc_now() -> datetime:
        """Get current time in UTC"""
        return datetime.utcnow().replace(tzinfo=pytz.UTC)
    
    @staticmethod
    def utc_to_ist(utc_dt: datetime) -> datetime:
        """Convert UTC datetime to IST"""
        if utc_dt.tzinfo is None:
            utc_dt = pytz.UTC.localize(utc_dt)
        return utc_dt.astimezone(IST)
    
    @staticmethod
    def ist_to_utc(ist_dt: datetime) -> datetime:
        """Convert IST datetime to UTC"""
        if ist_dt.tzinfo is None:
            ist_dt = IST.localize(ist_dt)
        return ist_dt.astimezone(pytz.UTC)
    
    @classmethod
    def calculate_order_prep_time(cls, order_items: List[Dict], active_items_by_kitchen: Dict[str, int] = None, kitchen_slot_availability: Dict[str, List[datetime]] = None) -> Dict:
        """
        Calculate intelligent preparation time with proper slot management
        
        Args:
            order_items: List of items with kitchen_id and prep_time
            active_items_by_kitchen: Currently active items per kitchen
            kitchen_slot_availability: When each slot becomes available per kitchen
            
        Returns:
            Dict with total_prep_time, kitchen_prep_times, estimated_completion (UTC), and buffer_applied
        """
        if not order_items:
            current_time = cls.get_utc_now()
            return {
                "total_prep_time": 0,
                "kitchen_prep_times": {},
                "estimated_completion": current_time,
                "estimated_completion_ist": cls.utc_to_ist(current_time),
                "buffer_applied": False
            }
        
        current_time = cls.get_utc_now()
        active_items = active_items_by_kitchen or {}
        slot_availability = kitchen_slot_availability or {}
        
        # Single item optimization
        if len(order_items) == 1:
            item = order_items[0]
            kitchen_id = item.get('kitchen_id', 'UNKNOWN')
            prep_time = item.get('prep_time', item.get('prep_time_minutes', 10))
            
            kitchen_prep_time = cls._calculate_single_item_prep_time(
                prep_time, kitchen_id, active_items.get(kitchen_id, 0),
                slot_availability.get(kitchen_id, [])
            )
            
            # Apply buffer for single item orders
            total_with_buffer = kitchen_prep_time + cls.ORDER_BUFFER_MINUTES
            completion_time = current_time + timedelta(minutes=total_with_buffer)
            
            return {
                "total_prep_time": total_with_buffer,
                "kitchen_prep_times": {kitchen_id: kitchen_prep_time},
                "estimated_completion": completion_time,
                "estimated_completion_ist": cls.utc_to_ist(completion_time),
                "buffer_applied": True
            }
        
        # Group items by kitchen
        kitchen_items = defaultdict(list)
        for item in order_items:
            kitchen_id = item.get('kitchen_id', 'UNKNOWN')
            prep_time = item.get('prep_time', item.get('prep_time_minutes', 10))
            kitchen_items[kitchen_id].append(prep_time)
        
        # Calculate prep time for each kitchen
        kitchen_prep_times = {}
        for kitchen_id, prep_times in kitchen_items.items():
            kitchen_prep_times[kitchen_id] = cls._calculate_kitchen_prep_time_with_slots(
                prep_times,
                active_items.get(kitchen_id, 0),
                slot_availability.get(kitchen_id, [])
            )
        
        # Total prep time is maximum across all kitchens (parallel processing)
        max_kitchen_time = max(kitchen_prep_times.values()) if kitchen_prep_times else 0
        total_with_buffer = max_kitchen_time + cls.ORDER_BUFFER_MINUTES
        completion_time = current_time + timedelta(minutes=total_with_buffer)
        
        return {
            "total_prep_time": total_with_buffer,
            "kitchen_prep_times": kitchen_prep_times,
            "estimated_completion": completion_time,
            "estimated_completion_ist": cls.utc_to_ist(completion_time),
            "buffer_applied": True
        }
    
    @classmethod
    def _calculate_single_item_prep_time(cls, prep_time: int, kitchen_id: str, active_items: int, slot_times: List[datetime]) -> int:
        """Calculate prep time for a single item considering slot availability"""
        if active_items < cls.MAX_PARALLEL_ITEMS:
            # Slot available, item starts immediately
            return prep_time
        
        # All slots occupied, find earliest available slot
        if slot_times:
            earliest_available = min(slot_times)
            current_time = cls.get_utc_now()
            wait_time = max(0, int((earliest_available - current_time).total_seconds() / 60))
            return wait_time + prep_time
        
        # Fallback: assume current items finish in average time
        estimated_wait = 8  # Average wait time when slots are full
        return estimated_wait + prep_time
    
    @classmethod
    def _calculate_kitchen_prep_time_with_slots(cls, prep_times: List[int], active_items: int, slot_times: List[datetime]) -> int:
        """Calculate kitchen prep time with proper slot management and batching"""
        if not prep_times:
            return 0
        
        # Sort prep times for optimal batching (longest first for better parallelization)
        sorted_times = sorted(prep_times, reverse=True)
        available_slots = max(0, cls.MAX_PARALLEL_ITEMS - active_items)
        current_time = cls.get_utc_now()
        
        # Initialize slot availability times
        slot_availability = [current_time] * cls.MAX_PARALLEL_ITEMS
        
        # Mark occupied slots with their estimated completion times
        if slot_times:
            for i, slot_time in enumerate(slot_times[:active_items]):
                slot_availability[i] = slot_time
        else:
            # Estimate when current active items will complete
            for i in range(active_items):
                slot_availability[i] = current_time + timedelta(minutes=8)  # Average completion time
        
        # Process items in batches
        total_completion_time = current_time
        remaining_items = sorted_times.copy()
        
        while remaining_items:
            # Find available slots (earliest completion times)
            slot_availability.sort()
            batch_start_time = slot_availability[0]
            
            # Determine batch size (available slots or remaining items, whichever is smaller)
            batch_size = min(len(remaining_items), cls.MAX_PARALLEL_ITEMS)
            batch_items = remaining_items[:batch_size]
            remaining_items = remaining_items[batch_size:]
            
            # Batch completion time is the maximum prep time in the batch
            batch_duration = max(batch_items)
            batch_completion_time = batch_start_time + timedelta(minutes=batch_duration)
            
            # Update slot availability times
            for i in range(batch_size):
                slot_availability[i] = batch_completion_time
            
            total_completion_time = max(total_completion_time, batch_completion_time)
        
        # Return total time in minutes from now
        return max(0, int((total_completion_time - current_time).total_seconds() / 60))
    
    @classmethod
    def get_kitchen_specific_prep_time(cls, kitchen_id: str, order_items: List[Dict], active_items_by_kitchen: Dict[str, int] = None) -> Dict:
        """Get preparation time for a specific kitchen only (for manager view)"""
        kitchen_items = [item for item in order_items if item.get('kitchen_id') == kitchen_id]
        
        if not kitchen_items:
            current_time = cls.get_utc_now()
            return {
                "kitchen_id": kitchen_id,
                "prep_time": 0,
                "estimated_completion": current_time,
                "estimated_completion_ist": cls.utc_to_ist(current_time),
                "items_count": 0
            }
        
        prep_times = [item.get('prep_time', item.get('prep_time_minutes', 10)) for item in kitchen_items]
        active_items = active_items_by_kitchen.get(kitchen_id, 0) if active_items_by_kitchen else 0
        
        kitchen_prep_time = cls._calculate_kitchen_prep_time_with_slots(prep_times, active_items, [])
        current_time = cls.get_utc_now()
        completion_time = current_time + timedelta(minutes=kitchen_prep_time)
        
        return {
            "kitchen_id": kitchen_id,
            "prep_time": kitchen_prep_time,
            "estimated_completion": completion_time,
            "estimated_completion_ist": cls.utc_to_ist(completion_time),
            "items_count": len(kitchen_items)
        }
    
    @classmethod
    def update_queue_timing(cls, queue_items: List[Dict], kitchen_capacities: Dict[str, int] = None) -> List[Dict]:
        """Update queue items with intelligent timing"""
        if not queue_items:
            return []
        
        # Group by kitchen and calculate active items
        kitchen_queues = defaultdict(list)
        active_counts = defaultdict(int)
        
        for item in queue_items:
            kitchen_id = item.get('kitchen_id', 'UNKNOWN')
            kitchen_queues[kitchen_id].append(item)
            
            if item.get('status') in ['in_progress', 'preparing']:
                active_counts[kitchen_id] += 1
        
        updated_items = []
        current_time = cls.get_ist_now()
        
        # Process each kitchen's queue
        for kitchen_id, items in kitchen_queues.items():
            # Sort by priority and scheduled time
            items.sort(key=lambda x: (
                x.get('priority_level', 'normal') != 'emergency',
                x.get('scheduled_start', current_time)
            ))
            
            kitchen_start_time = current_time
            active_items = active_counts[kitchen_id]
            batch_items = []
            
            for item in items:
                if item.get('status') == 'completed':
                    updated_items.append(item)
                    continue
                
                prep_time = item.get('prep_time', item.get('prep_time_minutes', 10))
                batch_items.append((item, prep_time))
                
                # Process batch when full or at end
                if len(batch_items) >= cls.MAX_PARALLEL_ITEMS or item == items[-1]:
                    # Calculate batch timing
                    if active_items > 0:
                        # Wait for active items to complete
                        batch_start = kitchen_start_time
                        active_items = 0
                    else:
                        batch_start = kitchen_start_time
                    
                    batch_duration = max(prep_time for _, prep_time in batch_items)
                    batch_end = batch_start + timedelta(minutes=batch_duration)
                    
                    # Update items in batch
                    for batch_item, item_prep_time in batch_items:
                        batch_item['scheduled_start'] = batch_start
                        batch_item['estimated_completion'] = batch_end
                        updated_items.append(batch_item)
                    
                    # Next batch starts after this one
                    kitchen_start_time = batch_end
                    batch_items = []
        
        return updated_items
    
    @staticmethod
    def format_time(minutes: int) -> str:
        """Format time in minutes to readable string"""
        if minutes < 60:
            return f"{minutes}m"
        hours = minutes // 60
        mins = minutes % 60
        return f"{hours}h {mins}m"
    
    @classmethod
    def format_ist_time(cls, dt: datetime) -> str:
        """Format datetime for IST display"""
        if dt.tzinfo is None:
            dt = pytz.UTC.localize(dt)
        ist_time = dt.astimezone(IST)
        return ist_time.strftime("%I:%M %p IST")


