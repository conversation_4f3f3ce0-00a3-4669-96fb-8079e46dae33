{"name": "kds-queue-management", "version": "1.0.0", "description": "Smart Kitchen Display System with AI-powered queue management", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["kds", "kitchen", "queue", "management", "ai", "restaurant", "pos"], "author": "KDS Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "socket.io": "^4.7.4", "redis": "^4.6.12", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "winston": "^3.11.0", "axios": "^1.6.2", "uuid": "^9.0.1", "dotenv": "^16.3.1", "express-async-errors": "^3.1.1"}, "devDependencies": {"@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prisma": "^5.7.1", "supertest": "^6.3.3", "@types/supertest": "^6.0.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}