# Kitchen Display System with AI Sequencing

A PostgreSQL-based order management system with AI-powered kitchen sequencing using Ollama Llama3.1.

## Features

- PostgreSQL database with Excel data seeding
- AI-powered kitchen sequencing using Ollama Llama3.1
- Order management from creation to delivery
- Kitchen configuration from Excel file

## Setup

1. **Install dependencies:**
```bash
pip install -r requirements.txt
```

2. **Setup PostgreSQL database:**
Update `.env` file with your database URL

3. **Install and start Ollama:**
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull Llama3.1 model
ollama pull llama3.1

# Start Ollama server (default: http://localhost:11434)
ollama serve
```

4. **Setup database and seed data:**
```bash
python setup_database.py
```

5. **Start the application:**
```bash
python main.py
```

## API Endpoints

### Orders
- `POST /api/orders/` - Create new order
- `GET /api/orders/` - Get all orders
- `GET /api/orders/{order_id}` - Get specific order
- `PUT /api/orders/{order_id}/status` - Update order status

### AI Sequencing
- `POST /api/ai/seed-database` - Seed database from Excel
- `POST /api/ai/train` - Train AI model with kitchen data
- `POST /api/ai/sequence/{order_id}` - Get AI sequence for order
- `GET /api/ai/training-data` - Get current training data

## Usage Flow

1. **Seed Database:** Load kitchen configuration from Excel
2. **Train AI:** Train Llama3.1 with kitchen data
3. **Create Orders:** Add orders with items
4. **Get AI Sequence:** Get optimized kitchen sequencing

## Test the System

```bash
python test_ai_system.py
```

## Excel File Structure

Place your `kitchen_config.xlsx` file in the root directory with sheets:
- `kitchens` or `Kitchen` - Kitchen configuration
- `menu_items` or `Menu` - Menu items with prep times

Required columns:
- Kitchen: kitchen_id, kitchen_name, capacity, specialization
- Menu: item_id, item_name, kitchen_id, prep_time_minutes, difficulty_level