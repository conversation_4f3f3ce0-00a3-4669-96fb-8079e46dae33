"""
Database setup and seeding script.
"""

import asyncio
import logging
from app.database.connection import init_database
from app.database.connection import get_db_session
from app.services.data_seeder import data_seeder

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def setup_database():
    """Setup database and seed data."""
    try:
        # Initialize database
        await init_database()
        logger.info("Database initialized")
        
        # Seed data from Excel
        async for db in get_db_session():
            await data_seeder.seed_from_excel(db)
            logger.info("Database seeded from Excel")
            break
            
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(setup_database())