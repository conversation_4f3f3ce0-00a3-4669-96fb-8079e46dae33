"""
Simplified Kitchen Display System - Order Management Only
"""

import logging
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.database.connection import init_database, close_database
from app.api import orders, system, ai

logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    try:
        logger.info("🚀 Initializing Kitchen Display System...")
        
        # Initialize database
        await init_database()
        
        logger.info("✅ System initialized successfully")
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize system: {e}")
        raise
    finally:
        await close_database()
        logger.info("System shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Kitchen Display System",
    description="Simple order management from creation to delivery",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "Internal server error"
        }
    )

@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Kitchen Display System",
        "version": "1.0.0",
        "status": "running"
    }

# Include API routers
app.include_router(orders.router, prefix="/api/orders", tags=["Orders"])
app.include_router(system.router, prefix="/api/system", tags=["System"])
app.include_router(ai.router, prefix="/api/ai", tags=["AI"])

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)