import winston from 'winston';
import path from 'path';
import fs from 'fs';
import { appConfig } from '@/config';

// Ensure logs directory exists
const logsDir = path.dirname(appConfig.logFile);
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let metaStr = '';
    if (Object.keys(meta).length > 0) {
      metaStr = ` ${JSON.stringify(meta)}`;
    }
    return `${timestamp} [${level}]: ${message}${metaStr}`;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
  level: appConfig.logLevel,
  format: fileFormat,
  defaultMeta: { service: 'kds-queue-management' },
  transports: [
    // File transport for all logs
    new winston.transports.File({
      filename: appConfig.logFile,
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    }),
    
    // Separate file for errors
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    })
  ],
  
  // Handle uncaught exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log')
    })
  ],
  
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log')
    })
  ]
});

// Add console transport for development
if (appConfig.nodeEnv !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

// Create child loggers for different modules
export const createModuleLogger = (module: string) => {
  return logger.child({ module });
};

// Specific loggers for different parts of the application
export const dbLogger = createModuleLogger('database');
export const redisLogger = createModuleLogger('redis');
export const aiLogger = createModuleLogger('ai-service');
export const queueLogger = createModuleLogger('queue-manager');
export const wsLogger = createModuleLogger('websocket');
export const apiLogger = createModuleLogger('api');

// Performance logging utility
export const logPerformance = (operation: string, startTime: number, metadata?: any) => {
  const duration = Date.now() - startTime;
  logger.info(`Performance: ${operation}`, {
    duration: `${duration}ms`,
    ...metadata
  });
};

// Request logging utility
export const logRequest = (method: string, url: string, statusCode: number, duration: number, userId?: string) => {
  apiLogger.info(`${method} ${url}`, {
    statusCode,
    duration: `${duration}ms`,
    userId
  });
};

// Error logging utility with context
export const logError = (error: Error, context?: any) => {
  logger.error('Application error', {
    message: error.message,
    stack: error.stack,
    context
  });
};

// Queue operation logging
export const logQueueOperation = (operation: string, kitchenId: string, itemId?: string, metadata?: any) => {
  queueLogger.info(`Queue operation: ${operation}`, {
    kitchenId,
    itemId,
    ...metadata
  });
};

// AI service logging
export const logAIOperation = (operation: string, duration: number, success: boolean, metadata?: any) => {
  aiLogger.info(`AI operation: ${operation}`, {
    duration: `${duration}ms`,
    success,
    ...metadata
  });
};

// WebSocket logging
export const logWebSocketEvent = (event: string, clientId?: string, metadata?: any) => {
  wsLogger.info(`WebSocket event: ${event}`, {
    clientId,
    ...metadata
  });
};

// Database operation logging
export const logDatabaseOperation = (operation: string, table: string, duration: number, metadata?: any) => {
  dbLogger.debug(`Database operation: ${operation}`, {
    table,
    duration: `${duration}ms`,
    ...metadata
  });
};

export default logger;
