"""
Ollama client for AI sequencing.
"""

import httpx
import json
import logging

logger = logging.getLogger(__name__)

class OllamaClient:
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.model = "llama3.1"
    
    async def generate_sequence(self, prompt: str) -> str:
        """Generate kitchen sequence using Ollama."""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/generate",
                    json={
                        "model": self.model,
                        "prompt": prompt,
                        "stream": False
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("response", "")
                else:
                    logger.error(f"Ollama API error: {response.status_code}")
                    return ""
                    
        except Exception as e:
            logger.error(f"Failed to call Ollama: {e}")
            return ""
    
    async def train_with_data(self, training_data: str) -> bool:
        """Train model with kitchen data."""
        try:
            prompt = f"""
            Learn this kitchen configuration data for order sequencing:
            {training_data}
            
            Remember this data for future kitchen sequencing tasks.
            """
            
            result = await self.generate_sequence(prompt)
            return len(result) > 0
            
        except Exception as e:
            logger.error(f"Failed to train model: {e}")
            return False

ollama_client = OllamaClient()