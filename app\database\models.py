"""
PostgreSQL models based on kitchen configuration Excel.
"""

from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Enum as SQLEnum, DECIMAL, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

Base = declarative_base()

class OrderStatus(enum.Enum):
    PENDING = "pending"
    CONFIRMED = "confirmed"
    PREPARING = "preparing"
    READY = "ready"
    DELIVERED = "delivered"

class Kitchen(Base):
    __tablename__ = "kitchens"
    
    id = Column(Integer, primary_key=True, index=True)
    kitchen_id = Column(String(50), unique=True, index=True, nullable=False)
    kitchen_name = Column(String(200), nullable=False)
    capacity = Column(Integer, default=3)
    specialization = Column(String(500))
    location = Column(String(200))
    equipment = Column(Text)
    
    menu_items = relationship("MenuItem", back_populates="kitchen")

class MenuItem(Base):
    __tablename__ = "menu_items"
    
    id = Column(Integer, primary_key=True, index=True)
    item_id = Column(String(50), unique=True, index=True, nullable=False)
    item_name = Column(String(200), nullable=False)
    kitchen_id = Column(String(50), ForeignKey("kitchens.kitchen_id"), nullable=False)
    prep_time_minutes = Column(Integer, nullable=False)
    difficulty_level = Column(String(50))
    category = Column(String(100))
    price = Column(DECIMAL(10, 2))
    ingredients = Column(Text)
    allergens = Column(Text)
    available = Column(Boolean, default=True)
    
    kitchen = relationship("Kitchen", back_populates="menu_items")

class Order(Base):
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(String(50), unique=True, index=True, nullable=False)
    customer_name = Column(String(200))
    table_number = Column(String(20))
    status = Column(SQLEnum(OrderStatus), default=OrderStatus.PENDING, index=True)
    total_amount = Column(DECIMAL(10, 2))
    notes = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")

class OrderItem(Base):
    __tablename__ = "order_items"
    
    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(String(50), ForeignKey("orders.order_id"), nullable=False)
    item_id = Column(String(50), ForeignKey("menu_items.item_id"), nullable=False)
    item_name = Column(String(200), nullable=False)
    quantity = Column(Integer, default=1)
    notes = Column(Text)
    
    order = relationship("Order", back_populates="items")
    menu_item = relationship("MenuItem")