// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum OrderStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum ItemStatus {
  QUEUED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum PriorityLevel {
  LOW
  NORMAL
  HIGH
  EMERGENCY
}

enum DifficultyLevel {
  EASY
  MEDIUM
  HARD
  EXPERT
}

enum KitchenStatus {
  ACTIVE
  INACTIVE
  MAINTENANCE
}

// Core Models
model MenuItem {
  id                String          @id @default(cuid())
  itemId            String          @unique @map("item_id")
  itemName          String          @map("item_name")
  kitchenId         String          @map("kitchen_id")
  prepTimeMinutes   Int             @map("prep_time_minutes")
  difficultyLevel   DifficultyLevel @map("difficulty_level")
  available         Boolean         @default(true)
  category          String?
  description       String?
  price             Decimal?        @db.Decimal(10, 2)
  allergens         String[]
  nutritionalInfo   Json?           @map("nutritional_info")
  createdAt         DateTime        @default(now()) @map("created_at")
  updatedAt         DateTime        @updatedAt @map("updated_at")

  // Relations
  kitchen    Kitchen     @relation(fields: [kitchenId], references: [kitchenId])
  queueItems QueueItem[]
  orderItems OrderItem[]

  @@map("menu_items")
}

model Kitchen {
  id          String        @id @default(cuid())
  kitchenId   String        @unique @map("kitchen_id")
  kitchenName String        @map("kitchen_name")
  capacity    Int           @default(3)
  status      KitchenStatus @default(ACTIVE)
  location    String?
  equipment   String[]
  createdAt   DateTime      @default(now()) @map("created_at")
  updatedAt   DateTime      @updatedAt @map("updated_at")

  // Relations
  menuItems  MenuItem[]
  queueItems QueueItem[]

  @@map("kitchens")
}

model Order {
  id            String      @id @default(cuid())
  orderId       String      @unique @map("order_id")
  customerName  String?     @map("customer_name")
  tableNumber   String?     @map("table_number")
  status        OrderStatus @default(PENDING)
  priority      PriorityLevel @default(NORMAL)
  totalAmount   Decimal?    @db.Decimal(10, 2) @map("total_amount")
  notes         String?
  estimatedTime Int?        @map("estimated_time") // in minutes
  actualTime    Int?        @map("actual_time") // in minutes
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @updatedAt @map("updated_at")
  completedAt   DateTime?   @map("completed_at")

  // Relations
  items      OrderItem[]
  queueItems QueueItem[]

  @@map("orders")
}

model OrderItem {
  id         String @id @default(cuid())
  orderId    String @map("order_id")
  menuItemId String @map("menu_item_id")
  quantity   Int    @default(1)
  notes      String?
  createdAt  DateTime @default(now()) @map("created_at")

  // Relations
  order    Order    @relation(fields: [orderId], references: [orderId])
  menuItem MenuItem @relation(fields: [menuItemId], references: [itemId])

  @@map("order_items")
}

model QueueItem {
  id                   String      @id @default(cuid())
  itemId               String      @map("item_id")
  itemName             String      @map("item_name")
  orderId              String      @map("order_id")
  kitchenId            String      @map("kitchen_id")
  status               ItemStatus  @default(QUEUED)
  priority             PriorityLevel @default(NORMAL)
  prepTime             Int         @map("prep_time") // in minutes
  sequenceNumber       Int?        @map("sequence_number")
  scheduledStart       DateTime    @map("scheduled_start")
  estimatedCompletion  DateTime    @map("estimated_completion")
  actualStart          DateTime?   @map("actual_start")
  actualCompletion     DateTime?   @map("actual_completion")
  notes                String?
  starvationCount      Int         @default(0) @map("starvation_count")
  lastStarvationCheck  DateTime?   @map("last_starvation_check")
  createdAt            DateTime    @default(now()) @map("created_at")
  updatedAt            DateTime    @updatedAt @map("updated_at")

  // Relations
  order   Order   @relation(fields: [orderId], references: [orderId])
  kitchen Kitchen @relation(fields: [kitchenId], references: [kitchenId])

  @@map("queue_items")
}

model PerformanceRecord {
  id                String   @id @default(cuid())
  kitchenId         String   @map("kitchen_id")
  itemId            String   @map("item_id")
  orderId           String   @map("order_id")
  estimatedTime     Int      @map("estimated_time") // in minutes
  actualTime        Int      @map("actual_time") // in minutes
  efficiency        Decimal  @db.Decimal(5, 2) // percentage
  completedAt       DateTime @map("completed_at")
  staffCount        Int?     @map("staff_count")
  rushHour          Boolean  @default(false) @map("rush_hour")
  dayOfWeek         Int      @map("day_of_week") // 0-6
  hourOfDay         Int      @map("hour_of_day") // 0-23
  createdAt         DateTime @default(now()) @map("created_at")

  @@map("performance_records")
}

// Indexes for better performance
@@index([kitchenId], map: "idx_queue_items_kitchen")
@@index([orderId], map: "idx_queue_items_order")
@@index([status], map: "idx_queue_items_status")
@@index([scheduledStart], map: "idx_queue_items_scheduled")
@@index([sequenceNumber], map: "idx_queue_items_sequence")
@@index([createdAt], map: "idx_queue_items_created")
@@index([kitchenId, status], map: "idx_queue_items_kitchen_status")
@@index([priority, scheduledStart], map: "idx_queue_items_priority_scheduled")
