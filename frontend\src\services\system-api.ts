/**
 * System API Service
 */

import { apiClient } from './api';

export interface SystemHealth {
  status: string;
  database: {
    status: string;
    version?: string;
    database_url?: string;
  };
  version: string;
}

export class SystemApiService {
  private readonly basePath = '/api/system';

  async getHealth(): Promise<SystemHealth> {
    return apiClient.get<SystemHealth>(`${this.basePath}/health`);
  }
}

export const systemApi = new SystemApiService();