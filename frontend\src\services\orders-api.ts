/**
 * Simplified Orders API Service
 */

import { apiClient } from './api';

export interface OrderItem {
  item_id: string;
  item_name: string;
  quantity: number;
  notes?: string;
}

export interface Order {
  order_id: string;
  customer_name?: string;
  table_number?: string;
  order_type: 'dine_in' | 'takeout' | 'delivery';
  items: OrderItem[];
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
  notes?: string;
  created_at: string;
  updated_at?: string;
}

export interface CreateOrderRequest {
  order_id: string;
  customer_name?: string;
  table_number?: string;
  order_type: 'dine_in' | 'takeout' | 'delivery';
  items: OrderItem[];
  notes?: string;
}

export class OrdersApiService {
  private readonly basePath = '/api/orders';

  async createOrder(orderData: CreateOrderRequest): Promise<Order> {
    return apiClient.post<Order>(this.basePath, orderData);
  }

  async getOrders(status?: string, limit: number = 100): Promise<Order[]> {
    const params = new URLSearchParams();
    if (status) params.append('status', status);
    params.append('limit', limit.toString());
    
    const url = `${this.basePath}?${params}`;
    return apiClient.get<Order[]>(url);
  }

  async getOrder(orderId: string): Promise<Order> {
    return apiClient.get<Order>(`${this.basePath}/${orderId}`);
  }

  async updateOrderStatus(orderId: string, status: string, notes?: string): Promise<Order> {
    return apiClient.put<Order>(`${this.basePath}/${orderId}/status`, {
      status,
      notes
    });
  }

  async confirmOrder(orderId: string): Promise<Order> {
    return apiClient.put<Order>(`${this.basePath}/${orderId}/confirm`);
  }

  async startPreparing(orderId: string): Promise<Order> {
    return apiClient.put<Order>(`${this.basePath}/${orderId}/start-preparing`);
  }

  async markReady(orderId: string): Promise<Order> {
    return apiClient.put<Order>(`${this.basePath}/${orderId}/ready`);
  }

  async deliverOrder(orderId: string): Promise<Order> {
    return apiClient.put<Order>(`${this.basePath}/${orderId}/deliver`);
  }

  // Convenience methods
  async getPendingOrders(): Promise<Order[]> {
    return this.getOrders('pending');
  }

  async getActiveOrders(): Promise<Order[]> {
    const [confirmed, preparing, ready] = await Promise.all([
      this.getOrders('confirmed'),
      this.getOrders('preparing'),
      this.getOrders('ready')
    ]);
    return [...confirmed, ...preparing, ...ready];
  }

  async getCompletedOrders(): Promise<Order[]> {
    return this.getOrders('delivered');
  }
}

export const ordersApi = new OrdersApiService();