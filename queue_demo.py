#!/usr/bin/env python3
"""
Demo: Queue Waiting Logic for Kitchen Parallel Processing
Shows how 6th order waits for slot to become available
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.prep_time_calculator import PrepTimeCalculator

def demo_queue_waiting():
    print("=== Kitchen Queue Waiting Demo ===")
    print("Kitchen has 5 parallel slots")
    print()
    
    # Scenario: 6 orders, each takes 10 minutes
    print("Scenario: 6 orders arrive, each needs 10 minutes prep time")
    order_items = [
        {"kitchen_id": "1", "prep_time": 10, "item_id": f"order_{i+1}"} 
        for i in range(6)
    ]
    
    result = PrepTimeCalculator.calculate_order_prep_time(order_items, {"1": 0})
    total_time = result['kitchen_prep_times']['1']
    
    print(f"Total completion time: {total_time} minutes")
    print()
    print("Timeline:")
    print("Time 0-10 mins: Orders 1,2,3,4,5 cooking in parallel (5 slots occupied)")
    print("Time 10 mins:   Orders 1,2,3,4,5 complete, Order 6 starts cooking")
    print("Time 20 mins:   Order 6 completes")
    print()
    print("Order 6 correctly waits for a slot to become available!")
    print(f"Expected: 20 minutes, Got: {total_time} minutes")
    
    # Show with active items
    print("\n" + "="*50)
    print("Scenario: 3 items already cooking + 6 new orders")
    
    result_active = PrepTimeCalculator.calculate_order_prep_time(order_items, {"1": 3})
    total_time_active = result_active['kitchen_prep_times']['1']
    
    print(f"Total completion time: {total_time_active} minutes")
    print()
    print("Timeline:")
    print("Time 0 mins:    3 items already cooking (3 slots occupied)")
    print("Time 0-10 mins: 2 new orders start immediately (5 slots total)")
    print("Time 5 mins:    Current items finish, 3 more orders start")
    print("Time 15 mins:   Next batch starts")
    print("Time 20 mins:   Final order completes")
    print()
    print("New orders wait for slots to become available!")
    print(f"Got: {total_time_active} minutes")

if __name__ == "__main__":
    demo_queue_waiting()